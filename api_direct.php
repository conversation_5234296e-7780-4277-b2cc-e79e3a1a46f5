<?php
// 性能优化 - 启用输出缓冲和压缩
if (!ob_get_level()) {
    ob_start('ob_gzhandler');
}

// 设置时区为北京时间
date_default_timezone_set('Asia/Shanghai');

// 性能优化 - 设置内存限制和执行时间
ini_set('memory_limit', '128M');
set_time_limit(30);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 性能优化 - 缓存控制
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    require_once 'backend/api/config.php';

    /**
     * 获取或创建用户的回收站账本
     */
    function getOrCreateRecycleBin($pdo, $userId) {
        // 先尝试获取现有的回收站账本
        $stmt = $pdo->prepare("
            SELECT * FROM account_books
            WHERE user_id = ? AND is_recycle_bin = 1
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $recycleBin = $stmt->fetch();

        if ($recycleBin) {
            return $recycleBin;
        }

        // 如果不存在，创建新的回收站账本
        $stmt = $pdo->prepare("
            INSERT INTO account_books (user_id, name, description, is_recycle_bin)
            VALUES (?, '🗑️ 回收站', '系统自动创建的回收站，用于存放已删除的记录', 1)
        ");
        $stmt->execute([$userId]);

        $recycleBinId = $pdo->lastInsertId();

        // 返回新创建的回收站账本
        $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
        $stmt->execute([$recycleBinId]);
        return $stmt->fetch();
    }

    $action = $_GET['action'] ?? $_POST['action'] ?? 'test';

    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'API连接正常',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;

        case 'login':
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';

            if (empty($username) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => '用户名和密码不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 查找用户
            $stmt = $pdo->prepare("SELECT id, username, email, password FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                http_response_code(401);
                echo json_encode(['error' => '用户名或密码错误']);
                exit;
            }

            // 生成JWT
            $payload = [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'exp' => time() + (7 * 24 * 60 * 60)
            ];
            $token = JWT::encode($payload);

            echo json_encode([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email']
                    ]
                ]
            ]);
            break;

        case 'register':
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';

            if (empty($username) || empty($email) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => '用户名、邮箱和密码不能为空']);
                exit;
            }

            if (strlen($password) < 6) {
                http_response_code(400);
                echo json_encode(['error' => '密码至少需要6位']);
                exit;
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                http_response_code(400);
                echo json_encode(['error' => '邮箱格式不正确']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 检查用户名是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => '用户名已存在']);
                exit;
            }

            // 检查邮箱是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => '邮箱已被注册']);
                exit;
            }

            // 创建新用户
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute([$username, $email, $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            // 生成JWT
            $payload = [
                'user_id' => $userId,
                'username' => $username,
                'exp' => time() + (7 * 24 * 60 * 60)
            ];
            $token = JWT::encode($payload);

            echo json_encode([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $userId,
                        'username' => $username,
                        'email' => $email
                    ]
                ]
            ]);
            break;

        case 'create_admin':
            $db = new Database();
            $pdo = $db->getConnection();

            // 删除现有admin用户
            $stmt = $pdo->prepare("DELETE FROM users WHERE username = 'admin'");
            $stmt->execute();

            // 创建新的admin用户
            $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            echo json_encode([
                'success' => true,
                'message' => 'admin用户创建成功',
                'data' => [
                    'user_id' => $userId,
                    'username' => 'admin',
                    'password' => 'password'
                ]
            ]);
            break;

        case 'init_monthly_system':
            $db = new Database();
            $pdo = $db->getConnection();

            // 创建月份状态表
            $createTableSQL = "
                CREATE TABLE IF NOT EXISTS record_monthly_states (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    record_id INT NOT NULL,
                    view_month VARCHAR(7) NOT NULL COMMENT '格式: 2024-05',
                    is_completed TINYINT(1) DEFAULT 0,
                    completed_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_record_month (record_id, view_month),
                    FOREIGN KEY (record_id) REFERENCES records(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='记录月份状态表'
            ";

            try {
                $pdo->exec($createTableSQL);
                echo json_encode([
                    'success' => true,
                    'message' => '月份系统初始化成功'
                ]);
            } catch (Exception $e) {
                http_response_code(500);
                echo json_encode([
                    'error' => '月份系统初始化失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'get_books':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            $stmt = $pdo->prepare("
                SELECT ab.*,
                       COUNT(r.id) as record_count,
                       CASE
                           WHEN ab.is_recycle_bin = 1 THEN COALESCE(SUM(ABS(r.amount)), 0)
                           ELSE COALESCE(SUM(r.accumulated_amount), 0)
                       END as total_amount
                FROM account_books ab
                LEFT JOIN records r ON ab.id = r.account_book_id
                WHERE ab.user_id = ?
                GROUP BY ab.id
                ORDER BY ab.created_at DESC
            ");
            $stmt->execute([$userId]);
            $books = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $books
            ]);
            break;

        case 'get_records':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_GET['book_id'] ?? '';
            // 使用北京时间获取当前月份
            $beijingTime = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
            $viewMonth = $_GET['view_month'] ?? $beijingTime->format('Y-m'); // 默认当前月

            if (empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本是否属于当前用户
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            // 获取记录，统一使用月份状态管理
            $currentMonth = $beijingTime->format('Y-m');

            // 检查是否为回收站账本
            $stmt = $pdo->prepare("SELECT is_recycle_bin FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);
            $bookInfo = $stmt->fetch();
            $isRecycleBin = $bookInfo && $bookInfo['is_recycle_bin'] == 1;

            // 修复BUG：统一使用月份状态表管理所有月份的状态
            // 如果是回收站账本，包含原账本信息
            if ($isRecycleBin) {
                $stmt = $pdo->prepare("
                    SELECT r.*,
                           COALESCE(rms.is_completed, 0) as current_completed,
                           rms.completed_at as monthly_completed_at,
                           ob.name as original_book_name
                    FROM records r
                    LEFT JOIN record_monthly_states rms ON r.id = rms.record_id AND rms.view_month = ?
                    LEFT JOIN account_books ob ON r.original_book_id = ob.id
                    WHERE r.account_book_id = ?
                    ORDER BY r.updated_at DESC
                ");
            } else {
                $stmt = $pdo->prepare("
                    SELECT r.*,
                           COALESCE(rms.is_completed, 0) as current_completed,
                           rms.completed_at as monthly_completed_at
                    FROM records r
                    LEFT JOIN record_monthly_states rms ON r.id = rms.record_id AND rms.view_month = ?
                    WHERE r.account_book_id = ?
                    ORDER BY r.created_at DESC
                ");
            }
            $stmt->execute([$viewMonth, $bookId]);
            $records = $stmt->fetchAll();

            // 修复BUG：移除自动迁移逻辑，防止历史状态错误继承到新月份
            // 新月份应该默认显示所有记录为未完成状态，只有用户主动操作时才创建月份状态记录
            // 这确保了每个月份的状态完全独立，不会相互影响

            // 计算累计金额（截止到查看月份）
            foreach ($records as &$record) {
                $record['accumulated_amount'] = calculateAccumulatedAmount($pdo, $record['id'], $viewMonth);
            }

            echo json_encode([
                'success' => true,
                'data' => $records,
                'view_month' => $viewMonth
            ]);
            break;

        case 'get_recycle_bin_records':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            try {
                // 获取用户的回收站账本
                $stmt = $pdo->prepare("SELECT id FROM account_books WHERE user_id = ? AND is_recycle_bin = 1");
                $stmt->execute([$userId]);
                $recycleBin = $stmt->fetch();

                if (!$recycleBin) {
                    echo json_encode([
                        'success' => true,
                        'data' => [],
                        'message' => '回收站为空'
                    ]);
                    exit;
                }

                // 获取回收站中的记录，包含原账本信息
                $stmt = $pdo->prepare("
                    SELECT r.*,
                           ab.name as book_name,
                           ob.name as original_book_name
                    FROM records r
                    JOIN account_books ab ON r.account_book_id = ab.id
                    LEFT JOIN account_books ob ON r.original_book_id = ob.id
                    WHERE r.account_book_id = ?
                    ORDER BY r.updated_at DESC
                ");
                $stmt->execute([$recycleBin['id']]);
                $records = $stmt->fetchAll();

                // 设置缓存控制头，确保数据实时性
                header('Cache-Control: no-cache, no-store, must-revalidate');
                header('Pragma: no-cache');
                header('Expires: 0');

                echo json_encode([
                    'success' => true,
                    'data' => $records
                ]);

            } catch (Exception $e) {
                echo json_encode(['error' => '获取回收站记录失败：' . $e->getMessage()]);
            }
            break;

        case 'add_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_POST['book_id'] ?? '';
            $date = $_POST['date'] ?? '';
            $name = $_POST['name'] ?? '';
            $amount = $_POST['amount'] ?? '';
            $monthlyAmount = $_POST['monthly_amount'] ?? '';
            $renewalTime = $_POST['renewal_time'] ?? '';
            $renewalAmount = $_POST['renewal_amount'] ?? '';
            $remark = $_POST['remark'] ?? '';
            $isDecreasing = $_POST['is_decreasing'] ?? '0';

            if (empty($bookId) || empty($date) || empty($name) || empty($amount) || empty($renewalTime)) {
                http_response_code(400);
                echo json_encode(['error' => '必填字段不能为空']);
                exit;
            }

            // 每月金额和续期金额如果不填默认为0
            if (empty($monthlyAmount)) {
                $monthlyAmount = 0;
            }
            if (empty($renewalAmount)) {
                $renewalAmount = 0;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本是否属于当前用户
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            // 初始累计金额为0，只有标记完成时才累计
            $accumulatedAmount = 0;

            // 递减形式的剩余金额初始化为总金额
            $remainingAmount = $isDecreasing == '1' ? $amount : 0;

            // 插入记录
            $stmt = $pdo->prepare("
                INSERT INTO records (account_book_id, date, name, amount, monthly_amount, renewal_time, renewal_amount, accumulated_amount, remark, is_completed, is_decreasing, remaining_amount, is_finished)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?, 0)
            ");
            $stmt->execute([
                $bookId, $date, $name, $amount, $monthlyAmount,
                $renewalTime, $renewalAmount, $accumulatedAmount, $remark,
                $isDecreasing, $remainingAmount
            ]);

            $recordId = $pdo->lastInsertId();

            // 获取新创建的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $record = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '记录添加成功',
                'data' => $record
            ]);
            break;

        case 'toggle_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 使用北京时间
            $beijingTime = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
            $currentMonth = $beijingTime->format('Y-m');

            // 修复BUG：统一使用月份状态表管理当前月份
            // 检查当前月份的月份状态
            $stmt = $pdo->prepare("
                SELECT * FROM record_monthly_states
                WHERE record_id = ? AND view_month = ?
            ");
            $stmt->execute([$recordId, $currentMonth]);
            $monthlyState = $stmt->fetch();

            // 修复BUG：移除自动迁移逻辑，防止历史状态错误继承
            // 如果没有月份状态记录，说明这是新月份，应该从未完成状态开始

            // 获取当前完成状态
            $currentCompleted = $monthlyState ? $monthlyState['is_completed'] : 0;
            $newStatus = $currentCompleted ? 0 : 1;



            // 更新或插入月份状态
            if ($monthlyState) {
                $stmt = $pdo->prepare("
                    UPDATE record_monthly_states
                    SET is_completed = ?, completed_at = ?, updated_at = NOW()
                    WHERE record_id = ? AND view_month = ?
                ");
                $completedAt = $newStatus ? date('Y-m-d H:i:s') : null;
                $stmt->execute([$newStatus, $completedAt, $recordId, $currentMonth]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                    VALUES (?, ?, ?, ?)
                ");
                $completedAt = $newStatus ? date('Y-m-d H:i:s') : null;
                $stmt->execute([$recordId, $currentMonth, $newStatus, $completedAt]);
            }

            // 修复BUG：不再更新原始记录状态，完全使用月份状态表管理
            // 这确保了状态的一致性，避免了全选功能的闪烁问题

            // 重新计算累计金额
            $newAccumulatedAmount = calculateAccumulatedAmount($pdo, $recordId, $currentMonth);

            echo json_encode([
                'success' => true,
                'message' => '状态更新成功',
                'data' => [
                    'is_completed' => $newStatus,
                    'accumulated_amount' => $newAccumulatedAmount
                ]
            ]);
            break;

        case 'handle_month_transition':
            // 验证token
            $headers = getallheaders();
            $authHeader = '';

            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $fromMonth = $_POST['from_month'] ?? '';
            $toMonth = $_POST['to_month'] ?? '';
            $bookId = $_POST['book_id'] ?? '';

            if (empty($fromMonth) || empty($toMonth) || empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '参数不完整']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本权限
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            try {
                $pdo->beginTransaction();

                // 获取该账本下所有已完成的记录
                $stmt = $pdo->prepare("
                    SELECT id, is_completed
                    FROM records
                    WHERE account_book_id = ? AND is_completed = 1
                ");
                $stmt->execute([$bookId]);
                $completedRecords = $stmt->fetchAll();

                $migratedCount = 0;

                // 将上个月的完成状态保存到月份状态表
                foreach ($completedRecords as $record) {
                    $recordId = $record['id'];

                    // 检查是否已存在月份状态
                    $stmt = $pdo->prepare("
                        SELECT id FROM record_monthly_states
                        WHERE record_id = ? AND view_month = ?
                    ");
                    $stmt->execute([$recordId, $fromMonth]);

                    if (!$stmt->fetch()) {
                        // 插入上个月的完成状态
                        $stmt = $pdo->prepare("
                            INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                            VALUES (?, ?, 1, NOW())
                        ");
                        $stmt->execute([$recordId, $fromMonth]);
                        $migratedCount++;
                    }
                }

                // 重置当前月份的记录状态（为新月份做准备）
                $stmt = $pdo->prepare("
                    UPDATE records SET
                        is_completed = 0,
                        completed_month = NULL
                    WHERE account_book_id = ? AND is_completed = 1
                ");
                $stmt->execute([$bookId]);

                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => "月份跨越处理完成，已保存 {$migratedCount} 条{$fromMonth}的完成状态",
                    'data' => [
                        'from_month' => $fromMonth,
                        'to_month' => $toMonth,
                        'migrated_count' => $migratedCount
                    ]
                ]);

            } catch (Exception $e) {
                $pdo->rollBack();
                http_response_code(500);
                echo json_encode(['error' => '月份跨越处理失败: ' . $e->getMessage()]);
            }
            break;

        case 'update_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            $date = $_POST['date'] ?? '';
            $name = $_POST['name'] ?? '';
            $amount = $_POST['amount'] ?? '';
            $monthlyAmount = $_POST['monthly_amount'] ?? '';
            $renewalTime = $_POST['renewal_time'] ?? '';
            $renewalAmount = $_POST['renewal_amount'] ?? '';
            $remark = $_POST['remark'] ?? '';
            $isDecreasing = $_POST['is_decreasing'] ?? '0';

            if (empty($recordId) || empty($date) || empty($name) || empty($amount) || empty($renewalTime)) {
                http_response_code(400);
                echo json_encode(['error' => '必填字段不能为空']);
                exit;
            }

            // 每月金额和续期金额如果不填默认为0
            if (empty($monthlyAmount)) {
                $monthlyAmount = 0;
            }
            if (empty($renewalAmount)) {
                $renewalAmount = 0;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 检查是否有累计金额参数
            $accumulatedAmount = $_POST['accumulated_amount'] ?? null;

            // 计算剩余金额（递减形式）- 修复逻辑确保数据一致性
            if ($isDecreasing == '1') {
                // 对于递减记账，剩余金额 = 原始金额 - 累计金额
                $currentAccumulated = $accumulatedAmount !== null ? floatval($accumulatedAmount) : 0;
                $remainingAmount = max(0, floatval($amount) - $currentAccumulated);
            } else {
                $remainingAmount = 0;
            }

            if ($accumulatedAmount !== null) {
                // 更新记录包含累计金额
                $stmt = $pdo->prepare("
                    UPDATE records
                    SET date = ?, name = ?, amount = ?, monthly_amount = ?, renewal_time = ?, renewal_amount = ?, remark = ?, accumulated_amount = ?, is_decreasing = ?, remaining_amount = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $date, $name, $amount, $monthlyAmount,
                    $renewalTime, $renewalAmount, $remark, $accumulatedAmount,
                    $isDecreasing, $remainingAmount, $recordId
                ]);
            } else {
                // 更新记录不包含累计金额 - 需要获取现有累计金额来计算剩余金额
                if ($isDecreasing == '1') {
                    // 获取当前累计金额
                    $stmt = $pdo->prepare("SELECT accumulated_amount FROM records WHERE id = ?");
                    $stmt->execute([$recordId]);
                    $currentRecord = $stmt->fetch();
                    $currentAccumulated = $currentRecord ? floatval($currentRecord['accumulated_amount']) : 0;
                    $remainingAmount = max(0, floatval($amount) - $currentAccumulated);
                }

                $stmt = $pdo->prepare("
                    UPDATE records
                    SET date = ?, name = ?, amount = ?, monthly_amount = ?, renewal_time = ?, renewal_amount = ?, remark = ?, is_decreasing = ?, remaining_amount = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $date, $name, $amount, $monthlyAmount,
                    $renewalTime, $renewalAmount, $remark,
                    $isDecreasing, $remainingAmount, $recordId
                ]);
            }

            // 获取更新后的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $updatedRecord = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '记录更新成功',
                'data' => $updatedRecord
            ]);
            break;

        case 'delete_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            try {
                $pdo->beginTransaction();

                // 验证记录权限（确保不是回收站账本）
                $stmt = $pdo->prepare("
                    SELECT r.*, ab.name as book_name, ab.is_recycle_bin
                    FROM records r
                    JOIN account_books ab ON r.account_book_id = ab.id
                    WHERE r.id = ? AND ab.user_id = ? AND ab.is_recycle_bin = 0
                ");
                $stmt->execute([$recordId, $userId]);
                $record = $stmt->fetch();

                if (!$record) {
                    echo json_encode(['error' => '记录不存在或无权限']);
                    exit;
                }

                // 获取或创建回收站账本
                $recycleBin = getOrCreateRecycleBin($pdo, $userId);

                // 移动记录到回收站而不是删除
                $stmt = $pdo->prepare("
                    UPDATE records SET
                        account_book_id = ?,
                        original_book_id = ?,
                        deleted_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$recycleBin['id'], $record['account_book_id'], $recordId]);

                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => '记录已移动到回收站',
                    'moved_to_recycle_bin' => true,
                    'original_book' => $record['book_name']
                ]);

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                echo json_encode(['error' => '操作失败：' . $e->getMessage()]);
            }
            break;

        case 'permanent_delete_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            try {
                $pdo->beginTransaction();

                // 验证记录权限（支持从普通账本或回收站删除）
                $stmt = $pdo->prepare("
                    SELECT r.*, ab.name as book_name, ab.is_recycle_bin
                    FROM records r
                    JOIN account_books ab ON r.account_book_id = ab.id
                    WHERE r.id = ? AND ab.user_id = ?
                ");
                $stmt->execute([$recordId, $userId]);
                $record = $stmt->fetch();

                if (!$record) {
                    echo json_encode(['error' => '记录不存在或无权限']);
                    exit;
                }

                // 根据记录所在位置决定删除方式
                if ($record['is_recycle_bin'] == 1) {
                    // 从回收站永久删除
                    $stmt = $pdo->prepare("DELETE FROM record_monthly_states WHERE record_id = ?");
                    $stmt->execute([$recordId]);

                    $stmt = $pdo->prepare("DELETE FROM records WHERE id = ?");
                    $stmt->execute([$recordId]);

                    $pdo->commit();

                    echo json_encode([
                        'success' => true,
                        'message' => '记录已从回收站永久删除，无法恢复',
                        'permanently_deleted' => true,
                        'original_book' => $record['book_name']
                    ]);
                } else {
                    // 从普通账本直接永久删除
                    $stmt = $pdo->prepare("DELETE FROM record_monthly_states WHERE record_id = ?");
                    $stmt->execute([$recordId]);

                    $stmt = $pdo->prepare("DELETE FROM records WHERE id = ?");
                    $stmt->execute([$recordId]);

                    $pdo->commit();

                    echo json_encode([
                        'success' => true,
                        'message' => '记录已永久删除，无法恢复',
                        'permanently_deleted' => true,
                        'original_book' => $record['book_name']
                    ]);
                }

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                echo json_encode(['error' => '操作失败：' . $e->getMessage()]);
            }
            break;

        case 'restore_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            try {
                $pdo->beginTransaction();

                // 验证记录权限（确保是回收站账本中的记录）
                $stmt = $pdo->prepare("
                    SELECT r.*, ab.name as book_name, r.original_book_id
                    FROM records r
                    JOIN account_books ab ON r.account_book_id = ab.id
                    WHERE r.id = ? AND ab.user_id = ? AND ab.is_recycle_bin = 1
                ");
                $stmt->execute([$recordId, $userId]);
                $record = $stmt->fetch();

                if (!$record) {
                    echo json_encode(['error' => '记录不存在、无权限或不在回收站中']);
                    exit;
                }

                // 获取原账本信息
                $originalBookId = $record['original_book_id'];
                $targetBookName = '未知账本';

                if ($originalBookId) {
                    // 检查原账本是否还存在
                    $stmt = $pdo->prepare("SELECT name FROM account_books WHERE id = ? AND user_id = ? AND is_recycle_bin = 0");
                    $stmt->execute([$originalBookId, $userId]);
                    $originalBook = $stmt->fetch();

                    if ($originalBook) {
                        $targetBookName = $originalBook['name'];
                    } else {
                        // 原账本不存在，恢复到默认账本
                        $stmt = $pdo->prepare("SELECT id, name FROM account_books WHERE user_id = ? AND is_recycle_bin = 0 ORDER BY created_at ASC LIMIT 1");
                        $stmt->execute([$userId]);
                        $defaultBook = $stmt->fetch();

                        if ($defaultBook) {
                            $originalBookId = $defaultBook['id'];
                            $targetBookName = $defaultBook['name'] . '（原账本已删除）';
                        } else {
                            echo json_encode(['error' => '没有可用的账本来恢复记录']);
                            exit;
                        }
                    }
                } else {
                    // 没有原账本信息，恢复到默认账本
                    $stmt = $pdo->prepare("SELECT id, name FROM account_books WHERE user_id = ? AND is_recycle_bin = 0 ORDER BY created_at ASC LIMIT 1");
                    $stmt->execute([$userId]);
                    $defaultBook = $stmt->fetch();

                    if ($defaultBook) {
                        $originalBookId = $defaultBook['id'];
                        $targetBookName = $defaultBook['name'] . '（默认账本）';
                    } else {
                        echo json_encode(['error' => '没有可用的账本来恢复记录']);
                        exit;
                    }
                }

                // 恢复记录到原账本
                $stmt = $pdo->prepare("
                    UPDATE records
                    SET account_book_id = ?, original_book_id = NULL, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$originalBookId, $recordId]);

                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => '记录已恢复',
                    'target_book' => $targetBookName
                ]);

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                echo json_encode(['error' => '操作失败：' . $e->getMessage()]);
            }
            break;

        case 'clear_recycle_bin':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            try {
                $pdo->beginTransaction();

                // 获取用户的回收站账本
                $stmt = $pdo->prepare("SELECT id FROM account_books WHERE user_id = ? AND is_recycle_bin = 1");
                $stmt->execute([$userId]);
                $recycleBin = $stmt->fetch();

                if (!$recycleBin) {
                    echo json_encode(['error' => '回收站不存在']);
                    exit;
                }

                // 获取回收站中的记录数量
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM records WHERE account_book_id = ?");
                $stmt->execute([$recycleBin['id']]);
                $count = $stmt->fetch()['count'];

                if ($count == 0) {
                    echo json_encode([
                        'success' => true,
                        'message' => '回收站已经是空的',
                        'deleted_count' => 0
                    ]);
                    exit;
                }

                // 删除回收站中所有记录的月份状态
                $stmt = $pdo->prepare("
                    DELETE rms FROM record_monthly_states rms
                    JOIN records r ON rms.record_id = r.id
                    WHERE r.account_book_id = ?
                ");
                $stmt->execute([$recycleBin['id']]);

                // 永久删除回收站中的所有记录
                $stmt = $pdo->prepare("DELETE FROM records WHERE account_book_id = ?");
                $stmt->execute([$recycleBin['id']]);

                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => '回收站已清空',
                    'deleted_count' => $count
                ]);

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                echo json_encode(['error' => '清空回收站失败：' . $e->getMessage()]);
            }
            break;

        case 'add_book':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            if (empty($name)) {
                http_response_code(400);
                echo json_encode(['error' => '账本名称不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 创建账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $name, $description]);

            $bookId = $pdo->lastInsertId();

            // 获取新创建的账本
            $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);
            $book = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '账本创建成功',
                'data' => $book
            ]);
            break;

        case 'delete_book':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_POST['book_id'] ?? '';
            if (empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本所有权并检查是否为回收站账本
            $stmt = $pdo->prepare("SELECT id, is_recycle_bin FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            $book = $stmt->fetch();

            if (!$book) {
                http_response_code(403);
                echo json_encode(['error' => '账本不存在或无权限']);
                exit;
            }

            // 禁止删除回收站账本
            if ($book['is_recycle_bin']) {
                http_response_code(400);
                echo json_encode(['error' => '不能删除回收站账本']);
                exit;
            }

            // 检查是否为用户唯一账本
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM account_books WHERE user_id = ?");
            $stmt->execute([$userId]);
            $count = $stmt->fetch()['count'];

            if ($count <= 1) {
                http_response_code(400);
                echo json_encode(['error' => '不能删除唯一的账本']);
                exit;
            }

            // 删除账本（会级联删除相关记录）
            $stmt = $pdo->prepare("DELETE FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);

            echo json_encode([
                'success' => true,
                'message' => '账本删除成功'
            ]);
            break;

        case 'change_password':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword)) {
                http_response_code(400);
                echo json_encode(['error' => '当前密码和新密码不能为空']);
                exit;
            }

            if (strlen($newPassword) < 6) {
                http_response_code(400);
                echo json_encode(['error' => '新密码至少需要6位']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 获取用户信息
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                http_response_code(404);
                echo json_encode(['error' => '用户不存在']);
                exit;
            }

            // 验证当前密码
            if (!password_verify($currentPassword, $user['password'])) {
                http_response_code(400);
                echo json_encode(['error' => '当前密码错误']);
                exit;
            }

            // 更新密码
            $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$hashedNewPassword, $userId]);

            echo json_encode([
                'success' => true,
                'message' => '密码修改成功'
            ]);
            break;

        case 'toggle_record_monthly':
            // 验证token
            $headers = getallheaders();
            $authHeader = '';
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }
            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            // 使用北京时间获取当前月份
            $beijingTime = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
            $viewMonth = $_POST['view_month'] ?? $beijingTime->format('Y-m');

            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录权限
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 获取当前月份状态
            $stmt = $pdo->prepare("
                SELECT * FROM record_monthly_states
                WHERE record_id = ? AND view_month = ?
            ");
            $stmt->execute([$recordId, $viewMonth]);
            $monthlyState = $stmt->fetch();

            $currentMonth = $beijingTime->format('Y-m');

            // 修复BUG：移除自动迁移逻辑，确保月份状态完全独立
            // 每个月份都应该从未完成状态开始，只有用户主动操作时才创建月份状态记录

            // 统一使用月份状态，如果没有则默认为0（未完成）
            $currentCompleted = $monthlyState ? $monthlyState['is_completed'] : 0;

            $newCompleted = $currentCompleted ? 0 : 1;

            // 检查是否为已结束的递减记录
            if ($record['is_decreasing'] && $record['is_finished']) {
                http_response_code(400);
                echo json_encode(['error' => '此记账已清零，请编辑或删除此记账']);
                exit;
            }

            // 计算递减逻辑（适用于所有月份的首次完成标记）
            $newAccumulatedAmount = floatval($record['accumulated_amount']);
            $newRemainingAmount = floatval($record['remaining_amount']);
            $isFinished = $record['is_finished'];

            if ($newCompleted == 1) {
                // 从未完成 → 完成：执行递减逻辑
                $isRenewalMonth = isRenewalMonthForDate($record, $viewMonth);
                $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                $newAccumulatedAmount += $addAmount;

                // 递减形式处理（任何月份的首次完成都要递减）
                if ($record['is_decreasing']) {
                    $newRemainingAmount -= $addAmount;
                    // 如果剩余金额小于等于0，标记为结束
                    if ($newRemainingAmount <= 0) {
                        $newRemainingAmount = 0; // 确保不为负数
                        $isFinished = 1;
                    }
                }
            } else {
                // 从完成 → 未完成：恢复递减逻辑
                $isRenewalMonth = isRenewalMonthForDate($record, $viewMonth);
                $subtractAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                $newAccumulatedAmount = max(0, $newAccumulatedAmount - $subtractAmount);

                // 递减形式处理（恢复递减状态）
                if ($record['is_decreasing']) {
                    $newRemainingAmount += $subtractAmount;
                    // 如果剩余金额大于0，取消结束状态
                    if ($newRemainingAmount > 0) {
                        $isFinished = 0;
                    }
                }
            }

            // 所有月份都使用统一的月份状态管理
            // 管理月份状态
            if ($monthlyState) {
                $stmt = $pdo->prepare("
                    UPDATE record_monthly_states
                    SET is_completed = ?, completed_at = ?, updated_at = NOW()
                    WHERE record_id = ? AND view_month = ?
                ");
                $completedAt = $newCompleted ? date('Y-m-d H:i:s') : null;
                $stmt->execute([$newCompleted, $completedAt, $recordId, $viewMonth]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                    VALUES (?, ?, ?, ?)
                ");
                $completedAt = $newCompleted ? date('Y-m-d H:i:s') : null;
                $stmt->execute([$recordId, $viewMonth, $newCompleted, $completedAt]);
            }

            // 如果是当前月份，同时更新记录的缓存字段（为了兼容性）
            if ($viewMonth === $currentMonth) {
                // 重新计算当前月份的累计金额和剩余金额
                $currentAccumulated = calculateAccumulatedAmount($pdo, $recordId, $currentMonth);
                $currentRemaining = floatval($record['amount']) - $currentAccumulated;
                $currentFinished = ($record['is_decreasing'] && $currentRemaining <= 0) ? 1 : 0;

                if ($record['is_decreasing']) {
                    $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, remaining_amount = ?, is_finished = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$newCompleted, $currentAccumulated, $currentRemaining, $currentFinished, $recordId]);
                } else {
                    $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$newCompleted, $currentAccumulated, $recordId]);
                }
            }

            // 获取最终的累计金额（所有月份都重新计算）
            $newAccumulatedAmount = calculateAccumulatedAmount($pdo, $recordId, $viewMonth);

            echo json_encode([
                'success' => true,
                'message' => '状态更新成功',
                'data' => [
                    'record_id' => $recordId,
                    'view_month' => $viewMonth,
                    'is_completed' => $newCompleted,
                    'accumulated_amount' => $newAccumulatedAmount
                ]
            ]);
            break;

        case 'fix_accumulated_amounts':
            // 修复累计金额的特殊操作
            $currentMonth = date('Y-m');

            // 获取所有记录
            $stmt = $pdo->prepare("SELECT * FROM records ORDER BY id");
            $stmt->execute();
            $records = $stmt->fetchAll();

            $fixedCount = 0;

            foreach ($records as $record) {
                // 重新计算当前月份的累计金额
                $correctAmount = calculateAccumulatedAmount($pdo, $record['id'], $currentMonth);

                // 更新记录
                $stmt = $pdo->prepare("UPDATE records SET accumulated_amount = ? WHERE id = ?");
                $stmt->execute([$correctAmount, $record['id']]);

                $fixedCount++;
            }

            echo json_encode([
                'success' => true,
                'message' => "已修复 {$fixedCount} 条记录的累计金额",
                'fixed_count' => $fixedCount
            ]);
            break;

        case 'clear_historical_states':
            // 清理历史月份状态记录的特殊操作
            $currentMonth = date('Y-m');

            // 删除所有历史月份的完成状态记录
            $stmt = $pdo->prepare("DELETE FROM record_monthly_states WHERE view_month < ?");
            $stmt->execute([$currentMonth]);
            $deletedCount = $stmt->rowCount();

            echo json_encode([
                'success' => true,
                'message' => "已清理 {$deletedCount} 条历史月份状态记录",
                'deleted_count' => $deletedCount
            ]);
            break;

        case 'batch_toggle_records':
            // 验证token
            $headers = getallheaders();
            $authHeader = '';

            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            // 获取参数并验证
            $recordIds = $_POST['record_ids'] ?? '';
            $targetState = $_POST['target_state'] ?? '';
            $viewMonth = $_POST['view_month'] ?? '';

            if (empty($recordIds) || $targetState === '' || empty($viewMonth)) {
                http_response_code(400);
                echo json_encode(['error' => '参数不完整']);
                exit;
            }

            // 验证viewMonth格式
            if (!preg_match('/^\d{4}-\d{2}$/', $viewMonth)) {
                http_response_code(400);
                echo json_encode(['error' => '月份格式无效']);
                exit;
            }

            // 验证targetState值
            if (!in_array($targetState, ['0', '1'])) {
                http_response_code(400);
                echo json_encode(['error' => '状态值无效']);
                exit;
            }

            // 解析记录ID数组并验证
            $recordIdArray = array_filter(array_map('intval', explode(',', $recordIds)));
            if (empty($recordIdArray)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID列表为空或无效']);
                exit;
            }

            // 限制批量操作数量
            if (count($recordIdArray) > 100) {
                http_response_code(400);
                echo json_encode(['error' => '批量操作数量不能超过100条']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 使用北京时间获取当前月份
            $beijingTime = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
            $currentMonth = $beijingTime->format('Y-m');

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            try {
                // 开始事务
                $pdo->beginTransaction();

                // 使用参数化查询验证记录权限
                $placeholders = str_repeat('?,', count($recordIdArray) - 1) . '?';
                $stmt = $pdo->prepare("
                    SELECT r.id
                    FROM records r
                    JOIN account_books ab ON r.account_book_id = ab.id
                    WHERE r.id IN ({$placeholders}) AND ab.user_id = ?
                ");
                $params = array_merge($recordIdArray, [$userId]);
                $stmt->execute($params);
                $validRecords = $stmt->fetchAll(PDO::FETCH_COLUMN);

                if (empty($validRecords)) {
                    $pdo->rollBack();
                    echo json_encode(['error' => '没有有效的记录可以操作']);
                    exit;
                }

                // 修复BUG：统一使用月份状态表管理，确保状态一致性
                // 所有月份都使用月份状态表，包括当前月份
                // 使用批量插入优化性能
                try {
                    if ($targetState == 1) {
                        // 批量插入/更新为完成状态
                        $values = [];
                        $params = [];
                        foreach ($validRecords as $recordId) {
                            $values[] = "(?, ?, 1, NOW())";
                            $params[] = $recordId;
                            $params[] = $viewMonth;
                        }
                        $valuesStr = implode(', ', $values);

                        $stmt = $pdo->prepare("
                            INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                            VALUES {$valuesStr}
                            ON DUPLICATE KEY UPDATE is_completed = 1, completed_at = NOW()
                        ");
                        $stmt->execute($params);
                    } else {
                        // 批量插入/更新为未完成状态
                        $values = [];
                        $params = [];
                        foreach ($validRecords as $recordId) {
                            $values[] = "(?, ?, 0, NULL)";
                            $params[] = $recordId;
                            $params[] = $viewMonth;
                        }
                        $valuesStr = implode(', ', $values);

                        $stmt = $pdo->prepare("
                            INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                            VALUES {$valuesStr}
                            ON DUPLICATE KEY UPDATE is_completed = 0, completed_at = NULL
                        ");
                        $stmt->execute($params);
                    }
                } catch (Exception $e) {
                    // 如果批量操作失败，回退到逐个处理
                    if ($targetState == 1) {
                        $stmt = $pdo->prepare("
                            INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                            VALUES (?, ?, 1, NOW())
                            ON DUPLICATE KEY UPDATE is_completed = 1, completed_at = NOW()
                        ");
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                            VALUES (?, ?, 0, NULL)
                            ON DUPLICATE KEY UPDATE is_completed = 0, completed_at = NULL
                        ");
                    }

                    foreach ($validRecords as $recordId) {
                        try {
                            $stmt->execute([$recordId, $viewMonth]);
                        } catch (Exception $e) {
                            $errors[] = "记录ID {$recordId}: " . $e->getMessage();
                            $errorCount++;
                        }
                    }
                }

                // 提交事务
                $pdo->commit();

                $successCount = count($validRecords) - $errorCount;
                $totalErrorCount = count($recordIdArray) - $successCount;

                echo json_encode([
                    'success' => true,
                    'message' => "批量操作完成",
                    'data' => [
                        'success_count' => $successCount,
                        'error_count' => $totalErrorCount,
                        'errors' => $errors
                    ]
                ]);

            } catch (Exception $e) {
                // 回滚事务
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                http_response_code(500);
                echo json_encode([
                    'error' => '批量操作失败: ' . $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
            break;

        case 'move_record':
            // 验证token
            $headers = getallheaders();
            $authHeader = '';
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }
            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            $targetBookId = $_POST['target_book_id'] ?? '';

            if (empty($recordId) || empty($targetBookId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID和目标账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录权限
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id, ab.name as book_name
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 验证目标账本权限
            $stmt = $pdo->prepare("SELECT id, name FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$targetBookId, $userId]);
            $targetBook = $stmt->fetch();

            if (!$targetBook) {
                http_response_code(403);
                echo json_encode(['error' => '目标账本不存在或无权限']);
                exit;
            }

            // 检查是否移动到同一个账本
            if ($record['account_book_id'] == $targetBookId) {
                http_response_code(400);
                echo json_encode(['error' => '不能移动到同一个账本']);
                exit;
            }

            try {
                // 开始事务
                $pdo->beginTransaction();

                // 移动记录到目标账本
                $stmt = $pdo->prepare("UPDATE records SET account_book_id = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$targetBookId, $recordId]);

                // 提交事务
                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => "记录已成功移动到账本「{$targetBook['name']}」",
                    'data' => [
                        'record_id' => $recordId,
                        'from_book' => $record['book_name'],
                        'to_book' => $targetBook['name']
                    ]
                ]);

            } catch (Exception $e) {
                // 回滚事务
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                http_response_code(500);
                echo json_encode(['error' => '移动记录失败: ' . $e->getMessage()]);
            }
            break;

        case 'check_table_structure':
            $db = new Database();
            $pdo = $db->getConnection();

            $table = $_POST['table'] ?? 'records';

            try {
                $stmt = $pdo->query("DESCRIBE `$table`");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'data' => $columns,
                    'message' => "表 $table 结构查询成功"
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '查询表结构失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'check_field_exists':
            $db = new Database();
            $pdo = $db->getConnection();

            $table = $_POST['table'] ?? 'records';
            $field = $_POST['field'] ?? 'repayment_date';

            try {
                $stmt = $pdo->query("SHOW COLUMNS FROM `$table` LIKE '$field'");
                $fieldInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'exists' => !empty($fieldInfo),
                    'field_info' => $fieldInfo,
                    'message' => $fieldInfo ? "字段 $field 存在" : "字段 $field 不存在"
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '检查字段失败: ' . $e->getMessage()
                ]);
            }
            break;



        case 'remove_field':
            $db = new Database();
            $pdo = $db->getConnection();

            $table = $_POST['table'] ?? 'records';
            $field = $_POST['field'] ?? 'repayment_date';

            try {
                // 先检查字段是否存在
                $stmt = $pdo->query("SHOW COLUMNS FROM `$table` LIKE '$field'");
                if (!$stmt->fetch()) {
                    echo json_encode([
                        'success' => false,
                        'message' => "字段 $field 不存在，无需删除"
                    ]);
                    break;
                }

                // 删除字段
                $pdo->exec("ALTER TABLE `$table` DROP COLUMN `$field`");

                echo json_encode([
                    'success' => true,
                    'message' => "字段 $field 删除成功"
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '删除字段失败: ' . $e->getMessage()
                ]);
            }
            break;

        default:
            http_response_code(404);
            echo json_encode(['error' => '未知操作']);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => '服务器错误',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

// 计算累计金额的函数
function calculateAccumulatedAmount($pdo, $recordId, $viewMonth) {
    // 获取记录信息
    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    if (!$record) {
        return 0;
    }

    $totalAmount = 0;
    // 使用北京时间获取当前月份
    $beijingTime = new DateTime('now', new DateTimeZone('Asia/Shanghai'));
    $currentMonth = $beijingTime->format('Y-m');

    // 优化：直接查询所有相关的月份状态，避免逐月查询
    $stmt = $pdo->prepare("
        SELECT rms.view_month, rms.is_completed
        FROM record_monthly_states rms
        WHERE rms.record_id = ? AND rms.view_month <= ? AND rms.is_completed = 1
        ORDER BY rms.view_month
    ");
    $stmt->execute([$recordId, $viewMonth]);
    $monthlyStates = $stmt->fetchAll();

    // 将月份状态转换为关联数组，便于查找
    $monthlyStateMap = [];
    foreach ($monthlyStates as $state) {
        $monthlyStateMap[$state['view_month']] = true;
    }

    // 计算月份状态的累计金额
    foreach ($monthlyStates as $state) {
        $monthStr = $state['view_month'];
        $isRenewalMonth = isRenewalMonthForDate($record, $monthStr);
        $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
        $totalAmount += $addAmount;
    }

    // 优化完成：移除了冗余的原始记录状态检查逻辑
    // 现在完全依赖月份状态表进行计算，确保数据一致性和逻辑简洁性
    // 备份文件：tools/calculateAccumulatedAmount_backup.php

    return $totalAmount;
}

// 判断指定月份是否为续期月份 - 统一前后端逻辑
function isRenewalMonthForDate($record, $monthStr) {
    $recordDate = $record['date'];
    $renewalTime = $record['renewal_time'];

    if (empty($renewalTime) || $renewalTime === '永久') {
        return false;
    }

    $monthsToAdd = 0;
    switch ($renewalTime) {
        case '一个月':
            $monthsToAdd = 1;
            break;
        case '二个月':
            $monthsToAdd = 2;
            break;
        case '三个月':
            $monthsToAdd = 3;
            break;
        case '六个月':
            $monthsToAdd = 6;
            break;
        default:
            return false;
    }

    // 使用与前端一致的数学计算方法
    $recordDateTime = new DateTime($recordDate);
    $viewDateTime = new DateTime($monthStr . '-01');

    $recordYear = (int)$recordDateTime->format('Y');
    $recordMonth = (int)$recordDateTime->format('n') - 1; // 转换为0-11的月份索引
    $viewYear = (int)$viewDateTime->format('Y');
    $viewMonth = (int)$viewDateTime->format('n') - 1; // 转换为0-11的月份索引

    // 计算月份差（与前端逻辑完全一致）
    $monthDiff = ($viewYear - $recordYear) * 12 + ($viewMonth - $recordMonth);

    // 检查是否为续期月份（包括创建月份和历史/未来续期月份）
    // 使用绝对值来处理历史月份，然后检查是否为续期间隔的倍数
    $isRenewal = abs($monthDiff) % $monthsToAdd === 0;

    return $isRenewal;
}

// 迁移当前月份状态到月份状态表
function migrateCurrentMonthState($pdo, $recordId, $currentMonth) {
    try {
        // 获取记录的当前状态
        $stmt = $pdo->prepare("SELECT is_completed FROM records WHERE id = ?");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch();

        if ($record && $record['is_completed'] == 1) {
            // 检查是否已存在月份状态
            $stmt = $pdo->prepare("
                SELECT id FROM record_monthly_states
                WHERE record_id = ? AND view_month = ?
            ");
            $stmt->execute([$recordId, $currentMonth]);

            if (!$stmt->fetch()) {
                // 插入月份状态记录
                $stmt = $pdo->prepare("
                    INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                    VALUES (?, ?, 1, NOW())
                ");
                $stmt->execute([$recordId, $currentMonth]);
            }
        }
    } catch (Exception $e) {
        error_log("迁移月份状态失败: " . $e->getMessage());
    }
}
?>
