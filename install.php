<?php
/**
 * 系统安装向导
 * 用于初始化数据库配置
 */

// 检查是否已经安装
$configFile = __DIR__ . '/backend/api/config.php';
$isInstalled = file_exists($configFile);

// 处理安装请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    $host = $_POST['host'] ?? '';
    $port = $_POST['port'] ?? '3306';
    $dbname = $_POST['dbname'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // 验证输入
    $errors = [];
    if (empty($host)) $errors[] = '数据库主机不能为空';
    if (empty($dbname)) $errors[] = '数据库名不能为空';
    if (empty($username)) $errors[] = '用户名不能为空';
    
    if (empty($errors)) {
        try {
            // 测试数据库连接
            $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            // 连接成功，创建配置文件
            $configContent = "<?php\n\n";
            $configContent .= "// 设置时区为北京时间\n";
            $configContent .= "date_default_timezone_set('Asia/Shanghai');\n\n";
            $configContent .= "// 数据库配置\n";
            $configContent .= "define('DB_HOST', '{$host}');\n";
            $configContent .= "define('DB_NAME', '{$dbname}');\n";
            $configContent .= "define('DB_USER', '{$username}');\n";
            $configContent .= "define('DB_PASS', '{$password}');\n";
            $configContent .= "define('DB_CHARSET', 'utf8mb4');\n\n";
            $configContent .= "// 其他配置...\n";
            
            // 确保目录存在
            $configDir = dirname($configFile);
            if (!is_dir($configDir)) {
                mkdir($configDir, 0755, true);
            }
            
            // 写入配置文件
            if (file_put_contents($configFile, $configContent)) {
                $success = "安装成功！数据库配置已保存。";
                $isInstalled = true;
            } else {
                $errors[] = "无法写入配置文件，请检查文件权限。";
            }
            
        } catch (PDOException $e) {
            $errors[] = "数据库连接失败: " . $e->getMessage();
        }
    }
}

// 获取当前配置（如果存在）
$currentConfig = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'dbname' => '',
    'username' => '',
    'password' => ''
];

if ($isInstalled && file_exists($configFile)) {
    $content = file_get_contents($configFile);
    if (preg_match("/define\('DB_HOST', '([^']*)'\);/", $content, $matches)) {
        $currentConfig['host'] = $matches[1];
    }
    if (preg_match("/define\('DB_NAME', '([^']*)'\);/", $content, $matches)) {
        $currentConfig['dbname'] = $matches[1];
    }
    if (preg_match("/define\('DB_USER', '([^']*)'\);/", $content, $matches)) {
        $currentConfig['username'] = $matches[1];
    }
    if (preg_match("/define\('DB_PASS', '([^']*)'\);/", $content, $matches)) {
        $currentConfig['password'] = $matches[1];
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统安装向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .status {
            text-align: center;
            padding: 20px;
        }
        
        .status.installed {
            color: #28a745;
        }
        
        .status.not-installed {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 系统安装向导</h1>
            <p>配置数据库连接信息</p>
        </div>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success">
                ✅ <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                ❌ <?php echo implode('<br>', array_map('htmlspecialchars', $errors)); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($isInstalled): ?>
            <div class="status installed">
                <h3>✅ 系统已安装</h3>
                <p>当前数据库配置:</p>
                <ul style="text-align: left; margin-top: 10px;">
                    <li><strong>主机:</strong> <?php echo htmlspecialchars($currentConfig['host']); ?></li>
                    <li><strong>数据库:</strong> <?php echo htmlspecialchars($currentConfig['dbname']); ?></li>
                    <li><strong>用户名:</strong> <?php echo htmlspecialchars($currentConfig['username']); ?></li>
                </ul>
                <p style="margin-top: 15px;">
                    <a href="index.html" style="color: #667eea; text-decoration: none;">← 返回主页</a>
                </p>
            </div>
        <?php else: ?>
            <div class="status not-installed">
                <h3>⚠️ 系统未安装</h3>
                <p>请填写数据库配置信息</p>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="host">数据库主机</label>
                    <input type="text" id="host" name="host" value="<?php echo htmlspecialchars($currentConfig['host']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="port">端口</label>
                    <input type="number" id="port" name="port" value="<?php echo htmlspecialchars($currentConfig['port']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="dbname">数据库名</label>
                    <input type="text" id="dbname" name="dbname" value="<?php echo htmlspecialchars($currentConfig['dbname']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($currentConfig['username']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" value="<?php echo htmlspecialchars($currentConfig['password']); ?>">
                </div>
                
                <button type="submit" name="install" class="btn">
                    安装系统
                </button>
            </form>
        <?php endif; ?>
    </div>
</body>
</html> 