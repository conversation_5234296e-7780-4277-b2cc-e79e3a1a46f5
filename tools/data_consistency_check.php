<?php
/**
 * 数据一致性检查和修复脚本
 * 用于检查和修复记账系统中的数据一致性问题
 */

require_once '../backend/api/config.php';

class DataConsistencyChecker {
    private $pdo;
    private $issues = [];
    private $fixes = [];

    public function __construct() {
        $db = new Database();
        $this->pdo = $db->getConnection();
    }

    /**
     * 执行完整的数据一致性检查
     */
    public function runFullCheck() {
        echo "开始数据一致性检查...\n\n";
        
        $this->checkRecordStateConsistency();
        $this->checkAccumulatedAmountConsistency();
        $this->checkDecreasingRecordConsistency();
        $this->checkOrphanedMonthlyStates();
        
        $this->generateReport();
    }

    /**
     * 检查记录状态一致性
     * records.is_completed 与 record_monthly_states 的一致性
     */
    private function checkRecordStateConsistency() {
        echo "检查记录状态一致性...\n";
        
        $currentMonth = date('Y-m');
        
        // 查找状态不一致的记录
        $stmt = $this->pdo->prepare("
            SELECT r.id, r.is_completed, 
                   COALESCE(rms.is_completed, 0) as monthly_completed,
                   r.name, r.date
            FROM records r
            LEFT JOIN record_monthly_states rms ON r.id = rms.record_id AND rms.view_month = ?
            WHERE r.is_completed != COALESCE(rms.is_completed, 0)
        ");
        $stmt->execute([$currentMonth]);
        $inconsistentRecords = $stmt->fetchAll();

        if (!empty($inconsistentRecords)) {
            $this->issues[] = [
                'type' => 'state_inconsistency',
                'count' => count($inconsistentRecords),
                'description' => '记录状态与月份状态不一致',
                'records' => $inconsistentRecords
            ];
            
            echo "发现 " . count($inconsistentRecords) . " 条状态不一致的记录\n";
        } else {
            echo "✅ 记录状态一致性检查通过\n";
        }
    }

    /**
     * 检查累计金额一致性
     */
    private function checkAccumulatedAmountConsistency() {
        echo "检查累计金额一致性...\n";
        
        $stmt = $this->pdo->query("SELECT * FROM records WHERE accumulated_amount > 0");
        $records = $stmt->fetchAll();
        
        $inconsistentAmounts = [];
        
        foreach ($records as $record) {
            $calculatedAmount = $this->calculateCorrectAccumulatedAmount($record['id']);
            $storedAmount = floatval($record['accumulated_amount']);
            
            if (abs($calculatedAmount - $storedAmount) > 0.01) { // 允许0.01的浮点误差
                $inconsistentAmounts[] = [
                    'id' => $record['id'],
                    'name' => $record['name'],
                    'stored_amount' => $storedAmount,
                    'calculated_amount' => $calculatedAmount,
                    'difference' => $calculatedAmount - $storedAmount
                ];
            }
        }

        if (!empty($inconsistentAmounts)) {
            $this->issues[] = [
                'type' => 'amount_inconsistency',
                'count' => count($inconsistentAmounts),
                'description' => '累计金额计算不一致',
                'records' => $inconsistentAmounts
            ];
            
            echo "发现 " . count($inconsistentAmounts) . " 条累计金额不一致的记录\n";
        } else {
            echo "✅ 累计金额一致性检查通过\n";
        }
    }

    /**
     * 检查递减记录一致性
     */
    private function checkDecreasingRecordConsistency() {
        echo "检查递减记录一致性...\n";
        
        $stmt = $this->pdo->query("
            SELECT * FROM records 
            WHERE is_decreasing = 1 AND remaining_amount > 0
        ");
        $decreasingRecords = $stmt->fetchAll();
        
        $inconsistentDecreasing = [];
        
        foreach ($decreasingRecords as $record) {
            $originalAmount = floatval($record['amount']);
            $accumulatedAmount = floatval($record['accumulated_amount']);
            $storedRemaining = floatval($record['remaining_amount']);
            $calculatedRemaining = max(0, $originalAmount - $accumulatedAmount);
            
            if (abs($calculatedRemaining - $storedRemaining) > 0.01) {
                $inconsistentDecreasing[] = [
                    'id' => $record['id'],
                    'name' => $record['name'],
                    'original_amount' => $originalAmount,
                    'accumulated_amount' => $accumulatedAmount,
                    'stored_remaining' => $storedRemaining,
                    'calculated_remaining' => $calculatedRemaining
                ];
            }
        }

        if (!empty($inconsistentDecreasing)) {
            $this->issues[] = [
                'type' => 'decreasing_inconsistency',
                'count' => count($inconsistentDecreasing),
                'description' => '递减记录剩余金额不一致',
                'records' => $inconsistentDecreasing
            ];
            
            echo "发现 " . count($inconsistentDecreasing) . " 条递减记录不一致\n";
        } else {
            echo "✅ 递减记录一致性检查通过\n";
        }
    }

    /**
     * 检查孤立的月份状态记录
     */
    private function checkOrphanedMonthlyStates() {
        echo "检查孤立的月份状态记录...\n";
        
        $stmt = $this->pdo->query("
            SELECT rms.* FROM record_monthly_states rms
            LEFT JOIN records r ON rms.record_id = r.id
            WHERE r.id IS NULL
        ");
        $orphanedStates = $stmt->fetchAll();

        if (!empty($orphanedStates)) {
            $this->issues[] = [
                'type' => 'orphaned_states',
                'count' => count($orphanedStates),
                'description' => '孤立的月份状态记录（对应的记录已被删除）',
                'records' => $orphanedStates
            ];
            
            echo "发现 " . count($orphanedStates) . " 条孤立的月份状态记录\n";
        } else {
            echo "✅ 月份状态记录检查通过\n";
        }
    }

    /**
     * 计算正确的累计金额
     */
    private function calculateCorrectAccumulatedAmount($recordId) {
        // 获取记录信息
        $stmt = $this->pdo->prepare("SELECT * FROM records WHERE id = ?");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch();

        if (!$record) {
            return 0;
        }

        $totalAmount = 0;
        $currentMonth = date('Y-m');

        // 查询所有已完成的月份状态
        $stmt = $this->pdo->prepare("
            SELECT view_month FROM record_monthly_states
            WHERE record_id = ? AND is_completed = 1 AND view_month <= ?
            ORDER BY view_month
        ");
        $stmt->execute([$recordId, $currentMonth]);
        $completedMonths = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // 计算每个已完成月份的金额
        foreach ($completedMonths as $month) {
            $isRenewal = $this->isRenewalMonthForRecord($record, $month);
            $amount = $isRenewal ? 
                floatval($record['renewal_amount']) : 
                floatval($record['monthly_amount']);
            $totalAmount += $amount;
        }

        return $totalAmount;
    }

    /**
     * 判断指定月份是否为续期月份
     */
    private function isRenewalMonthForRecord($record, $monthStr) {
        $recordDate = $record['date'];
        $renewalTime = $record['renewal_time'];

        if (empty($renewalTime) || $renewalTime === '永久') {
            return false;
        }

        $monthsToAdd = 0;
        switch ($renewalTime) {
            case '一个月': $monthsToAdd = 1; break;
            case '二个月': $monthsToAdd = 2; break;
            case '三个月': $monthsToAdd = 3; break;
            case '六个月': $monthsToAdd = 6; break;
            default: return false;
        }

        $recordDateTime = new DateTime($recordDate);
        $viewDateTime = new DateTime($monthStr . '-01');

        $recordYear = (int)$recordDateTime->format('Y');
        $recordMonth = (int)$recordDateTime->format('n') - 1;
        $viewYear = (int)$viewDateTime->format('Y');
        $viewMonth = (int)$viewDateTime->format('n') - 1;

        $monthDiff = ($viewYear - $recordYear) * 12 + ($viewMonth - $recordMonth);
        return abs($monthDiff) % $monthsToAdd === 0;
    }

    /**
     * 生成检查报告
     */
    private function generateReport() {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "数据一致性检查报告\n";
        echo str_repeat("=", 50) . "\n";

        if (empty($this->issues)) {
            echo "✅ 恭喜！没有发现数据一致性问题。\n";
            return;
        }

        echo "发现以下问题：\n\n";
        
        foreach ($this->issues as $issue) {
            echo "❌ {$issue['description']}: {$issue['count']} 条记录\n";
            
            if ($issue['type'] === 'amount_inconsistency') {
                echo "   详细信息：\n";
                foreach (array_slice($issue['records'], 0, 5) as $record) {
                    echo "   - ID:{$record['id']} {$record['name']} ";
                    echo "存储:{$record['stored_amount']} 计算:{$record['calculated_amount']} ";
                    echo "差异:{$record['difference']}\n";
                }
                if (count($issue['records']) > 5) {
                    echo "   ... 还有 " . (count($issue['records']) - 5) . " 条记录\n";
                }
            }
            echo "\n";
        }

        echo "建议：\n";
        echo "1. 运行修复脚本：php data_consistency_fix.php\n";
        echo "2. 备份数据库后再执行修复操作\n";
        echo "3. 修复后重新运行检查确认问题已解决\n";
    }

    /**
     * 获取检查结果
     */
    public function getIssues() {
        return $this->issues;
    }
}

// 如果直接运行此脚本
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $checker = new DataConsistencyChecker();
    $checker->runFullCheck();
}
?>
