<?php
/**
 * 数据库配置管理API接口
 * 为数据库工具管理中心提供统一的配置管理服务
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入配置管理器
require_once __DIR__ . '/database_config_manager.php';

// 响应函数
function sendResponse($success, $data = null, $message = null) {
    $response = ['success' => $success];
    
    if ($message !== null) {
        $response['message'] = $message;
    }
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 错误处理函数
function handleError($message, $details = null) {
    error_log("Database Config API Error: $message" . ($details ? " - Details: $details" : ""));
    sendResponse(false, null, $message);
}

// 获取请求数据
function getRequestData() {
    $data = [];
    
    // 从GET参数获取
    $data = array_merge($data, $_GET);
    
    // 从POST参数获取
    $data = array_merge($data, $_POST);
    
    // 从JSON输入获取
    $jsonInput = file_get_contents('php://input');
    if (!empty($jsonInput)) {
        $jsonData = json_decode($jsonInput, true);
        if (is_array($jsonData)) {
            $data = array_merge($data, $jsonData);
        }
    }
    
    return $data;
}

try {
    $manager = new DatabaseConfigManager();
    $requestData = getRequestData();
    $action = $requestData['action'] ?? null;
    
    // 调试信息
    error_log("API Request - Action: $action, Data: " . json_encode($requestData));
    
    switch ($action) {
        case 'get_current_config':
            // 获取当前所有配置文件的配置信息
            $configs = $manager->getCurrentConfig();
            sendResponse(true, $configs, '获取当前配置成功');
            break;
            
        case 'update_all_configs':
            // 更新所有配置文件
            $newConfig = [];
            
            // 尝试从config子对象获取
            if (isset($requestData['config']) && is_array($requestData['config'])) {
                $newConfig = $requestData['config'];
            } else {
                // 直接从请求数据获取
                $newConfig = [
                    'host' => $requestData['host'] ?? '',
                    'port' => $requestData['port'] ?? '3306',
                    'database' => $requestData['database'] ?? '',
                    'username' => $requestData['username'] ?? '',
                    'password' => $requestData['password'] ?? ''
                ];
            }
            
            // 调试信息
            error_log("Update Config - Final config: " . json_encode($newConfig));
            
            // 执行更新
            $result = $manager->updateAllConfigs($newConfig);
            
            if ($result['success']) {
                sendResponse(true, $result, '所有数据库配置已成功更新');
            } else {
                sendResponse(false, $result, $result['error']);
            }
            break;
            
        case 'test_connection':
            // 测试数据库连接
            $config = [];
            
            // 尝试从config子对象获取
            if (isset($requestData['config']) && is_array($requestData['config'])) {
                $config = $requestData['config'];
            } else {
                // 直接从请求数据获取
                $config = [
                    'host' => $requestData['host'] ?? '',
                    'port' => $requestData['port'] ?? '3306',
                    'database' => $requestData['database'] ?? '',
                    'username' => $requestData['username'] ?? '',
                    'password' => $requestData['password'] ?? ''
                ];
            }
            
            // 调试信息
            error_log("Test Connection - Final config: " . json_encode($config));
            
            $result = $manager->testConnection($config);
            sendResponse($result['success'], $result, $result['message']);
            break;
            
        case 'list_backups':
            // 列出配置备份
            $backups = $manager->listBackups();
            sendResponse(true, $backups, '获取备份列表成功');
            break;
            
        case 'get_config_files':
            // 获取所有配置文件的状态
            $configFiles = [
                'backend/config/database.php' => [
                    'name' => 'Laravel配置文件',
                    'description' => '主应用数据库配置',
                    'type' => 'laravel_config'
                ],
                'backend/api/config.php' => [
                    'name' => 'API配置文件',
                    'description' => 'API接口数据库配置',
                    'type' => 'api_config'
                ],
                'quick_backup.php' => [
                    'name' => '备份脚本配置',
                    'description' => '备份管理器数据库配置',
                    'type' => 'backup_script'
                ],
                'install.php' => [
                    'name' => '安装脚本配置',
                    'description' => '系统安装时的默认配置',
                    'type' => 'install_script'
                ]
            ];
            
            // 检查文件是否存在
            $projectRoot = dirname(__DIR__);
            foreach ($configFiles as $filePath => &$fileInfo) {
                $fullPath = $projectRoot . '/' . $filePath;
                $fileInfo['exists'] = file_exists($fullPath);
                $fileInfo['path'] = $filePath;
                
                if ($fileInfo['exists']) {
                    $fileInfo['last_modified'] = date('Y-m-d H:i:s', filemtime($fullPath));
                    $fileInfo['size'] = filesize($fullPath);
                }
            }
            
            sendResponse(true, $configFiles, '获取配置文件列表成功');
            break;
            
        default:
            handleError('无效的操作类型: ' . ($action ?: '未指定'));
    }
    
} catch (Exception $e) {
    handleError('系统错误: ' . $e->getMessage());
}
?> 