// 虚拟滚动组件

/**
 * 虚拟滚动类 - 优化大量数据的渲染性能
 */
class VirtualScroll {
    constructor(options) {
        this.container = options.container;
        this.itemHeight = options.itemHeight || 80;
        this.renderItem = options.renderItem;
        this.data = [];
        this.visibleStart = 0;
        this.visibleEnd = 0;
        this.scrollTop = 0;
        this.containerHeight = 0;
        this.totalHeight = 0;
        this.visibleCount = 0;
        this.bufferSize = options.bufferSize || 5; // 缓冲区大小
        
        this.init();
    }
    
    /**
     * 初始化虚拟滚动
     */
    init() {
        this.setupContainer();
        this.bindEvents();
        this.updateDimensions();
    }
    
    /**
     * 设置容器样式
     */
    setupContainer() {
        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';
        
        // 创建内容容器
        this.contentContainer = document.createElement('div');
        this.contentContainer.style.position = 'absolute';
        this.contentContainer.style.top = '0';
        this.contentContainer.style.left = '0';
        this.contentContainer.style.right = '0';
        
        // 创建占位容器（用于撑开滚动条）
        this.spacerContainer = document.createElement('div');
        this.spacerContainer.style.position = 'relative';
        
        this.container.appendChild(this.spacerContainer);
        this.container.appendChild(this.contentContainer);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        this.container.addEventListener('scroll', this.handleScroll.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }
    
    /**
     * 处理滚动事件
     */
    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        this.updateVisibleRange();
        this.render();
    }
    
    /**
     * 处理窗口大小变化
     */
    handleResize() {
        this.updateDimensions();
        this.updateVisibleRange();
        this.render();
    }
    
    /**
     * 更新容器尺寸
     */
    updateDimensions() {
        this.containerHeight = this.container.clientHeight;
        this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
        this.totalHeight = this.data.length * this.itemHeight;
        this.spacerContainer.style.height = this.totalHeight + 'px';
    }
    
    /**
     * 更新可见范围
     */
    updateVisibleRange() {
        const start = Math.floor(this.scrollTop / this.itemHeight);
        const end = Math.min(start + this.visibleCount + this.bufferSize * 2, this.data.length);
        
        this.visibleStart = Math.max(0, start - this.bufferSize);
        this.visibleEnd = end;
    }
    
    /**
     * 渲染可见项
     */
    render() {
        const fragment = document.createDocumentFragment();
        
        // 清空内容容器
        this.contentContainer.innerHTML = '';
        
        // 渲染可见项
        for (let i = this.visibleStart; i < this.visibleEnd; i++) {
            if (i >= this.data.length) break;
            
            const item = this.data[i];
            const itemElement = this.createItemElement(item, i);
            
            // 设置项的位置
            itemElement.style.position = 'absolute';
            itemElement.style.top = (i * this.itemHeight) + 'px';
            itemElement.style.left = '0';
            itemElement.style.right = '0';
            itemElement.style.height = this.itemHeight + 'px';
            
            fragment.appendChild(itemElement);
        }
        
        this.contentContainer.appendChild(fragment);
    }
    
    /**
     * 创建项元素
     */
    createItemElement(item, index) {
        const element = document.createElement('div');
        element.className = 'virtual-scroll-item';
        element.dataset.index = index;
        
        // 调用用户提供的渲染函数
        if (this.renderItem) {
            const content = this.renderItem(item, index);
            if (typeof content === 'string') {
                element.innerHTML = content;
            } else {
                element.appendChild(content);
            }
        }
        
        return element;
    }
    
    /**
     * 设置数据
     */
    setData(data) {
        this.data = data || [];
        this.updateDimensions();
        this.updateVisibleRange();
        this.render();
    }
    
    /**
     * 添加数据
     */
    addData(newData) {
        this.data = this.data.concat(newData);
        this.updateDimensions();
        this.updateVisibleRange();
        this.render();
    }
    
    /**
     * 更新单个项
     */
    updateItem(index, newData) {
        if (index >= 0 && index < this.data.length) {
            this.data[index] = newData;
            
            // 如果项在可见范围内，重新渲染
            if (index >= this.visibleStart && index < this.visibleEnd) {
                this.render();
            }
        }
    }
    
    /**
     * 删除项
     */
    removeItem(index) {
        if (index >= 0 && index < this.data.length) {
            this.data.splice(index, 1);
            this.updateDimensions();
            this.updateVisibleRange();
            this.render();
        }
    }
    
    /**
     * 滚动到指定项
     */
    scrollToItem(index) {
        if (index >= 0 && index < this.data.length) {
            const targetScrollTop = index * this.itemHeight;
            this.container.scrollTop = targetScrollTop;
        }
    }
    
    /**
     * 滚动到顶部
     */
    scrollToTop() {
        this.container.scrollTop = 0;
    }
    
    /**
     * 滚动到底部
     */
    scrollToBottom() {
        this.container.scrollTop = this.totalHeight;
    }
    
    /**
     * 获取当前可见项的索引范围
     */
    getVisibleRange() {
        return {
            start: this.visibleStart,
            end: this.visibleEnd
        };
    }
    
    /**
     * 销毁虚拟滚动
     */
    destroy() {
        this.container.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
        this.container.innerHTML = '';
    }
}

/**
 * 记录虚拟滚动组件 - 专门用于记录列表
 */
class RecordVirtualScroll extends VirtualScroll {
    constructor(container, options = {}) {
        super({
            container,
            itemHeight: options.itemHeight || 120,
            bufferSize: options.bufferSize || 3,
            renderItem: (record, index) => this.renderRecord(record, index)
        });
        
        this.onRecordClick = options.onRecordClick;
        this.onRecordToggle = options.onRecordToggle;
        this.onRecordEdit = options.onRecordEdit;
        this.onRecordDelete = options.onRecordDelete;
    }
    
    /**
     * 渲染记录项
     */
    renderRecord(record, index) {
        const recordEl = document.createElement('div');
        recordEl.className = 'record-card virtual-record-item';
        
        // 根据记录状态添加CSS类
        const cardClasses = ['record-card', 'virtual-record-item'];
        if (record.current_completed) {
            cardClasses.push('completed');
        }
        if (this.isRenewalMonth && this.isRenewalMonth(record)) {
            cardClasses.push('renewal-month');
        }
        recordEl.className = cardClasses.join(' ');
        
        // 创建记录内容
        const content = this.createRecordContent(record);
        recordEl.innerHTML = content;
        
        // 绑定事件
        this.bindRecordEvents(recordEl, record, index);
        
        return recordEl;
    }
    
    /**
     * 创建记录内容HTML
     */
    createRecordContent(record) {
        const isFinished = record.is_decreasing == 1 && record.is_finished == 1;
        const checkboxDisabled = isFinished ? 'disabled' : '';
        const finishedStyle = isFinished ? 'opacity: 0.6; background: #f5f5f5;' : '';
        
        return `
            <div class="record-header" style="${finishedStyle}">
                <div class="left-section">
                    <input type="checkbox" ${record.current_completed ? 'checked' : ''} ${checkboxDisabled}
                           data-action="toggle" onclick="event.stopPropagation()">
                    <span class="record-name">${sanitizeHTML(record.name || '')}</span>
                    <div class="record-amount">¥${parseFloat(record.amount || 0).toFixed(2)}</div>
                    <div class="accumulated">累计:¥${parseFloat(record.accumulated_amount || 0).toFixed(2)}</div>
                </div>
                <div class="right-section">
                    <button data-action="edit" onclick="event.stopPropagation()">✏️</button>
                    <button data-action="delete" onclick="event.stopPropagation()">🗑️</button>
                </div>
            </div>
            <div class="record-details">
                <div>${formatDate(record.date)}</div>
                <div>月:¥${parseFloat(record.monthly_amount || 0).toFixed(2)}</div>
                ${record.remark ? `<div>备注:${sanitizeHTML(record.remark)}</div>` : ''}
            </div>
        `;
    }
    
    /**
     * 绑定记录事件
     */
    bindRecordEvents(recordEl, record, index) {
        // 点击记录切换完成状态
        recordEl.addEventListener('click', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'BUTTON') return;
            if (this.onRecordClick) {
                this.onRecordClick(record, index);
            }
        });
        
        // 复选框切换
        const checkbox = recordEl.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.addEventListener('change', (e) => {
                e.stopPropagation();
                if (this.onRecordToggle) {
                    this.onRecordToggle(record, index, e.target.checked);
                }
            });
        }
        
        // 编辑按钮
        const editBtn = recordEl.querySelector('[data-action="edit"]');
        if (editBtn) {
            editBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (this.onRecordEdit) {
                    this.onRecordEdit(record, index);
                }
            });
        }
        
        // 删除按钮
        const deleteBtn = recordEl.querySelector('[data-action="delete"]');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (this.onRecordDelete) {
                    this.onRecordDelete(record, index);
                }
            });
        }
    }
    
    /**
     * 设置续期判断函数
     */
    setRenewalChecker(isRenewalMonth) {
        this.isRenewalMonth = isRenewalMonth;
    }
}

// 导出虚拟滚动组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        VirtualScroll,
        RecordVirtualScroll
    };
}
