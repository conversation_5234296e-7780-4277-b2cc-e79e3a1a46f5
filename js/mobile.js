// 移动端优化模块

/**
 * 移动端手势管理器
 */
class GestureManager {
    constructor(element) {
        this.element = element;
        this.startX = 0;
        this.startY = 0;
        this.endX = 0;
        this.endY = 0;
        this.startTime = 0;
        this.endTime = 0;
        this.threshold = 50; // 滑动阈值
        this.timeThreshold = 300; // 时间阈值
        
        this.init();
    }
    
    init() {
        this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true });
        this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    }
    
    handleTouchStart(e) {
        const touch = e.touches[0];
        this.startX = touch.clientX;
        this.startY = touch.clientY;
        this.startTime = Date.now();
    }
    
    handleTouchMove(e) {
        // 可以在这里处理拖拽逻辑
    }
    
    handleTouchEnd(e) {
        const touch = e.changedTouches[0];
        this.endX = touch.clientX;
        this.endY = touch.clientY;
        this.endTime = Date.now();
        
        this.detectGesture();
    }
    
    detectGesture() {
        const deltaX = this.endX - this.startX;
        const deltaY = this.endY - this.startY;
        const deltaTime = this.endTime - this.startTime;
        
        // 检查是否为有效滑动
        if (Math.abs(deltaX) > this.threshold || Math.abs(deltaY) > this.threshold) {
            if (deltaTime < this.timeThreshold) {
                // 确定滑动方向
                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                    // 水平滑动
                    if (deltaX > 0) {
                        this.onSwipeRight();
                    } else {
                        this.onSwipeLeft();
                    }
                } else {
                    // 垂直滑动
                    if (deltaY > 0) {
                        this.onSwipeDown();
                    } else {
                        this.onSwipeUp();
                    }
                }
            }
        } else if (deltaTime < 200) {
            // 快速点击
            this.onTap();
        }
    }
    
    onSwipeLeft() {
        this.element.dispatchEvent(new CustomEvent('swipeleft'));
    }
    
    onSwipeRight() {
        this.element.dispatchEvent(new CustomEvent('swiperight'));
    }
    
    onSwipeUp() {
        this.element.dispatchEvent(new CustomEvent('swipeup'));
    }
    
    onSwipeDown() {
        this.element.dispatchEvent(new CustomEvent('swipedown'));
    }
    
    onTap() {
        this.element.dispatchEvent(new CustomEvent('tap'));
    }
}

/**
 * 移动端下拉刷新组件
 */
class PullToRefresh {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            threshold: 60,
            maxPull: 120,
            onRefresh: options.onRefresh || (() => {}),
            ...options
        };
        
        this.startY = 0;
        this.currentY = 0;
        this.pulling = false;
        this.refreshing = false;
        
        this.init();
    }
    
    init() {
        this.createRefreshIndicator();
        this.bindEvents();
    }
    
    createRefreshIndicator() {
        this.indicator = document.createElement('div');
        this.indicator.className = 'pull-refresh-indicator';
        this.indicator.style.cssText = `
            position: absolute;
            top: -60px;
            left: 0;
            right: 0;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            color: #666;
            font-size: 14px;
            transition: transform 0.2s ease;
            z-index: 100;
        `;
        this.indicator.innerHTML = '下拉刷新';
        
        this.container.style.position = 'relative';
        this.container.insertBefore(this.indicator, this.container.firstChild);
    }
    
    bindEvents() {
        this.container.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        this.container.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        this.container.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
    }
    
    handleTouchStart(e) {
        if (this.container.scrollTop === 0) {
            this.startY = e.touches[0].clientY;
            this.pulling = true;
        }
    }
    
    handleTouchMove(e) {
        if (!this.pulling || this.refreshing) return;
        
        this.currentY = e.touches[0].clientY;
        const pullDistance = this.currentY - this.startY;
        
        if (pullDistance > 0 && this.container.scrollTop === 0) {
            e.preventDefault();
            
            const distance = Math.min(pullDistance * 0.5, this.options.maxPull);
            this.updateIndicator(distance);
        }
    }
    
    handleTouchEnd(e) {
        if (!this.pulling || this.refreshing) return;
        
        const pullDistance = this.currentY - this.startY;
        
        if (pullDistance > this.options.threshold) {
            this.startRefresh();
        } else {
            this.resetIndicator();
        }
        
        this.pulling = false;
    }
    
    updateIndicator(distance) {
        this.indicator.style.transform = `translateY(${distance}px)`;
        
        if (distance > this.options.threshold) {
            this.indicator.innerHTML = '释放刷新';
            this.indicator.style.color = '#1890ff';
        } else {
            this.indicator.innerHTML = '下拉刷新';
            this.indicator.style.color = '#666';
        }
    }
    
    startRefresh() {
        this.refreshing = true;
        this.indicator.style.transform = `translateY(${this.options.threshold}px)`;
        this.indicator.innerHTML = '正在刷新...';
        this.indicator.style.color = '#1890ff';
        
        // 调用刷新回调
        Promise.resolve(this.options.onRefresh()).finally(() => {
            this.endRefresh();
        });
    }
    
    endRefresh() {
        this.refreshing = false;
        this.resetIndicator();
    }
    
    resetIndicator() {
        this.indicator.style.transform = 'translateY(0)';
        this.indicator.innerHTML = '下拉刷新';
        this.indicator.style.color = '#666';
    }
}

/**
 * 移动端底部操作栏
 */
class MobileActionBar {
    constructor(container, actions = []) {
        this.container = container;
        this.actions = actions;
        this.visible = false;
        
        this.init();
    }
    
    init() {
        this.createActionBar();
        this.bindEvents();
    }
    
    createActionBar() {
        this.actionBar = document.createElement('div');
        this.actionBar.className = 'mobile-action-bar';
        this.actionBar.style.cssText = `
            position: fixed;
            bottom: -80px;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
            transition: bottom 0.3s ease;
            z-index: 1000;
        `;
        
        this.actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'action-btn';
            button.style.cssText = `
                background: ${action.color || '#1890ff'};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                cursor: pointer;
                flex: 1;
                margin: 0 4px;
                transition: all 0.2s ease;
            `;
            button.textContent = action.text;
            button.addEventListener('click', action.onClick);
            
            this.actionBar.appendChild(button);
        });
        
        document.body.appendChild(this.actionBar);
    }
    
    bindEvents() {
        // 监听选择变化
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            if (selection.rangeCount > 0 && !selection.isCollapsed) {
                this.show();
            } else {
                this.hide();
            }
        });
    }
    
    show() {
        if (!this.visible) {
            this.visible = true;
            this.actionBar.style.bottom = '0';
        }
    }
    
    hide() {
        if (this.visible) {
            this.visible = false;
            this.actionBar.style.bottom = '-80px';
        }
    }
    
    updateActions(actions) {
        this.actions = actions;
        this.actionBar.innerHTML = '';
        
        this.actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'action-btn';
            button.style.cssText = `
                background: ${action.color || '#1890ff'};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                cursor: pointer;
                flex: 1;
                margin: 0 4px;
                transition: all 0.2s ease;
            `;
            button.textContent = action.text;
            button.addEventListener('click', action.onClick);
            
            this.actionBar.appendChild(button);
        });
    }
}

/**
 * 移动端优化工具
 */
class MobileOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.optimizeViewport();
        this.optimizeScrolling();
        this.optimizeTouch();
        this.optimizeKeyboard();
    }
    
    /**
     * 优化视口设置
     */
    optimizeViewport() {
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    }
    
    /**
     * 优化滚动性能
     */
    optimizeScrolling() {
        // 启用硬件加速滚动
        document.body.style.webkitOverflowScrolling = 'touch';
        document.body.style.overscrollBehavior = 'contain';
        
        // 优化滚动容器
        const scrollContainers = document.querySelectorAll('.scroll-container, .records-container');
        scrollContainers.forEach(container => {
            container.style.webkitOverflowScrolling = 'touch';
            container.style.overscrollBehavior = 'contain';
            container.style.transform = 'translateZ(0)';
            container.style.willChange = 'scroll-position';
        });
    }
    
    /**
     * 优化触摸体验
     */
    optimizeTouch() {
        // 移除点击延迟
        document.addEventListener('touchstart', () => {}, { passive: true });
        
        // 优化触摸目标大小
        const buttons = document.querySelectorAll('button, .btn, .clickable');
        buttons.forEach(button => {
            const style = window.getComputedStyle(button);
            const minSize = 44; // iOS推荐的最小触摸目标
            
            if (parseInt(style.height) < minSize) {
                button.style.minHeight = minSize + 'px';
            }
            if (parseInt(style.width) < minSize) {
                button.style.minWidth = minSize + 'px';
            }
        });
    }
    
    /**
     * 优化虚拟键盘
     */
    optimizeKeyboard() {
        // 监听虚拟键盘显示/隐藏
        let initialViewportHeight = window.innerHeight;
        
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDiff = initialViewportHeight - currentHeight;
            
            if (heightDiff > 150) {
                // 虚拟键盘显示
                document.body.classList.add('keyboard-open');
                this.adjustForKeyboard(true);
            } else {
                // 虚拟键盘隐藏
                document.body.classList.remove('keyboard-open');
                this.adjustForKeyboard(false);
            }
        });
        
        // 输入框获得焦点时滚动到可见区域
        const inputs = document.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                setTimeout(() => {
                    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });
        });
    }
    
    /**
     * 调整键盘显示时的布局
     */
    adjustForKeyboard(isOpen) {
        const fixedElements = document.querySelectorAll('.fixed-bottom, .mobile-action-bar');
        fixedElements.forEach(element => {
            if (isOpen) {
                element.style.display = 'none';
            } else {
                element.style.display = '';
            }
        });
    }
    
    /**
     * 检测设备类型
     */
    static getDeviceInfo() {
        const userAgent = navigator.userAgent;
        const isIOS = /iPad|iPhone|iPod/.test(userAgent);
        const isAndroid = /Android/.test(userAgent);
        const isMobile = /Mobi|Android/i.test(userAgent);
        const isTablet = /iPad/.test(userAgent) || (isAndroid && !/Mobile/.test(userAgent));
        
        return {
            isIOS,
            isAndroid,
            isMobile,
            isTablet,
            isDesktop: !isMobile && !isTablet,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height,
            pixelRatio: window.devicePixelRatio || 1
        };
    }
    
    /**
     * 获取安全区域信息
     */
    static getSafeAreaInsets() {
        const style = getComputedStyle(document.documentElement);
        return {
            top: parseInt(style.getPropertyValue('--sat') || '0'),
            right: parseInt(style.getPropertyValue('--sar') || '0'),
            bottom: parseInt(style.getPropertyValue('--sab') || '0'),
            left: parseInt(style.getPropertyValue('--sal') || '0')
        };
    }
}

// 自动初始化移动端优化
if (isMobile()) {
    document.addEventListener('DOMContentLoaded', () => {
        new MobileOptimizer();
    });
}

// 导出移动端模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        GestureManager,
        PullToRefresh,
        MobileActionBar,
        MobileOptimizer
    };
}
