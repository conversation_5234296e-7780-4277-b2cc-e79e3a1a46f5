<?php
/**
 * 快速备份脚本
 * 支持数据库和文件的完整备份
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 备份配置
$config = [
    'backup_dir' => __DIR__ . '/Backup',
    'project_dir' => __DIR__,
    'database' => [
        'host' => '1Panel-mysql-dPoE',
        'port' => '3306',
        'name' => 'shuju',
        'user' => 'shuju',
        'pass' => 'Abc112211'
    ],
    'exclude_patterns' => [
        'Backup/*',
        'logs/*',
        '.git/*',
        '.vscode/*',
        'node_modules/*',
        '*.tmp',
        '*.log',
        '.DS_Store',
        'Thumbs.db'
    ]
];

// 获取备份类型
$backupType = $argv[1] ?? 'quick';

// 日志函数
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] [$level] $message\n";
}

// 创建备份目录
function createBackupDirectory($config) {
    $backupName = 'backup_' . date('Y-m-d_H-i-s');
    $backupPath = $config['backup_dir'] . '/' . $backupName;
    
    if (!is_dir($config['backup_dir'])) {
        mkdir($config['backup_dir'], 0755, true);
    }
    
    if (!mkdir($backupPath, 0755, true)) {
        throw new Exception("无法创建备份目录: $backupPath");
    }
    
    return [$backupName, $backupPath];
}

// 备份数据库
function backupDatabase($config, $backupPath) {
    logMessage("开始备份数据库...");
    
    $dbConfig = $config['database'];
    $sqlFile = $backupPath . '/database.sql';
    
    try {
        // 首先尝试使用mysqldump命令
        $command = sprintf(
            'mysqldump -h%s -P%s -u%s -p%s --single-transaction --routines --triggers %s > %s 2>&1',
            escapeshellarg($dbConfig['host']),
            escapeshellarg($dbConfig['port']),
            escapeshellarg($dbConfig['user']),
            escapeshellarg($dbConfig['pass']),
            escapeshellarg($dbConfig['name']),
            escapeshellarg($sqlFile)
        );
        
        exec($command, $output, $returnCode);
        
        // 如果mysqldump失败，使用PHP内置方法
        if ($returnCode !== 0 || !file_exists($sqlFile) || filesize($sqlFile) === 0) {
            logMessage("mysqldump不可用，使用PHP内置方法备份数据库...");
            return backupDatabaseWithPHP($dbConfig, $sqlFile);
        }
        
        $fileSize = formatBytes(filesize($sqlFile));
        logMessage("数据库备份完成: $fileSize");
        
        return $sqlFile;
        
    } catch (Exception $e) {
        logMessage("mysqldump失败，尝试使用PHP内置方法: " . $e->getMessage());
        return backupDatabaseWithPHP($dbConfig, $sqlFile);
    }
}

// 使用PHP内置方法备份数据库
function backupDatabaseWithPHP($dbConfig, $sqlFile) {
    try {
        // 创建PDO连接
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        $sql = "-- MySQL数据库备份\n";
        $sql .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- 数据库: {$dbConfig['name']}\n\n";
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n";
        $sql .= "SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO';\n\n";
        
        // 获取所有表
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            logMessage("备份表: $table");
            
            // 获取表结构
            $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch();
            $sql .= "-- 表结构: $table\n";
            $sql .= "DROP TABLE IF EXISTS `$table`;\n";
            $sql .= $createTable['Create Table'] . ";\n\n";
            
            // 获取表数据
            $stmt = $pdo->query("SELECT * FROM `$table`");
            $rowCount = 0;
            
            while ($row = $stmt->fetch()) {
                if ($rowCount === 0) {
                    $sql .= "-- 表数据: $table\n";
                    $columns = array_keys($row);
                    $sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
                }
                
                $values = array_map(function($value) use ($pdo) {
                    return $value === null ? 'NULL' : $pdo->quote($value);
                }, array_values($row));
                
                $sql .= "(" . implode(', ', $values) . ")";
                $rowCount++;
                
                // 每1000行写入一次，避免内存溢出
                if ($rowCount % 1000 === 0) {
                    $sql .= ";\n\nINSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
                } else {
                    $sql .= ",\n";
                }
            }
            
            if ($rowCount > 0) {
                $sql = rtrim($sql, ",\n") . ";\n\n";
            }
            
            logMessage("表 $table 备份完成: $rowCount 行");
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        // 写入文件
        if (file_put_contents($sqlFile, $sql) === false) {
            throw new Exception("无法写入SQL文件");
        }
        
        $fileSize = formatBytes(filesize($sqlFile));
        logMessage("数据库备份完成 (PHP方法): $fileSize");
        
        return $sqlFile;
        
    } catch (PDOException $e) {
        throw new Exception("数据库连接失败: " . $e->getMessage());
    } catch (Exception $e) {
        throw new Exception("数据库备份失败: " . $e->getMessage());
    }
}

// 备份文件
function backupFiles($config, $backupPath, $backupType) {
    logMessage("开始备份文件...");
    
    $sourceDir = $config['project_dir'];
    $excludePatterns = $config['exclude_patterns'];
    
    // 根据备份类型选择文件
    $filesToBackup = [];
    
    if ($backupType === 'quick') {
        // 快速备份：只备份核心文件
        $coreFiles = [
            'index.html',
            'api_direct.php',
            'backup_api.php',
            'backup_manager.html',
            'download_backup.php',
            'quick_backup.php',
            'auto_start.sh',
            'auto_backup.sh',
            'start.sh',
            '.htaccess',
            'install.php'
        ];
        
        foreach ($coreFiles as $file) {
            $filePath = $sourceDir . '/' . $file;
            if (file_exists($filePath)) {
                $filesToBackup[] = $file;
            }
        }
    } else {
        // 完整备份：备份所有文件（除了排除的）
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($sourceDir, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            $relativePath = substr($file->getPathname(), strlen($sourceDir) + 1);
            
            // 检查是否应该排除
            $shouldExclude = false;
            foreach ($excludePatterns as $pattern) {
                if (fnmatch($pattern, $relativePath)) {
                    $shouldExclude = true;
                    break;
                }
            }
            
            if (!$shouldExclude && $file->isFile()) {
                $filesToBackup[] = $relativePath;
            }
        }
    }
    
    // 复制文件
    $copiedCount = 0;
    $totalSize = 0;
    
    foreach ($filesToBackup as $relativePath) {
        $sourcePath = $sourceDir . '/' . $relativePath;
        $destPath = $backupPath . '/' . $relativePath;
        
        // 创建目标目录
        $destDir = dirname($destPath);
        if (!is_dir($destDir)) {
            mkdir($destDir, 0755, true);
        }
        
        if (copy($sourcePath, $destPath)) {
            $copiedCount++;
            $totalSize += filesize($sourcePath);
        } else {
            logMessage("警告: 无法复制文件 $relativePath", 'WARN');
        }
    }
    
    logMessage("文件备份完成: $copiedCount 个文件, " . formatBytes($totalSize));
    
    return $copiedCount;
}

// 创建备份信息文件
function createBackupInfo($config, $backupPath, $backupName, $backupType, $stats) {
    $backupInfo = [
        'backup_name' => $backupName,
        'backup_type' => $backupType,
        'created_at' => date('Y-m-d H:i:s'),
        'created_by' => 'quick_backup.php',
        'database_backup' => isset($stats['database_file']),
        'files_count' => $stats['files_count'] ?? 0,
        'total_size' => $stats['total_size'] ?? 0,
        'database_size' => $stats['database_size'] ?? 0,
        'config' => [
            'exclude_patterns' => $config['exclude_patterns'],
            'database_host' => $config['database']['host'],
            'database_name' => $config['database']['name']
        ]
    ];
    
    $infoFile = $backupPath . '/backup_info.json';
    file_put_contents(
        $infoFile,
        json_encode($backupInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
    );
    
    // 创建简单的文本信息文件
    $textInfo = "备份信息\n";
    $textInfo .= "========\n";
    $textInfo .= "备份名称: {$backupInfo['backup_name']}\n";
    $textInfo .= "备份类型: {$backupInfo['backup_type']}\n";
    $textInfo .= "创建时间: {$backupInfo['created_at']}\n";
    $textInfo .= "文件数量: {$backupInfo['files_count']}\n";
    $textInfo .= "总大小: " . formatBytes($backupInfo['total_size']) . "\n";
    
    if ($backupInfo['database_backup']) {
        $textInfo .= "数据库: 已备份 (" . formatBytes($backupInfo['database_size']) . ")\n";
    }
    
    file_put_contents($backupPath . '/backup_info.txt', $textInfo);
    
    return $backupInfo;
}

// 格式化字节大小
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// 计算目录大小
function calculateDirectorySize($path) {
    $size = 0;
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    
    return $size;
}

// 清理旧备份
function cleanupOldBackups($config, $keepCount = 10) {
    logMessage("清理旧备份...");
    
    $backupDir = $config['backup_dir'];
    if (!is_dir($backupDir)) {
        return;
    }
    
    $backups = [];
    $items = scandir($backupDir);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $itemPath = $backupDir . '/' . $item;
        if (is_dir($itemPath) && strpos($item, 'backup_') === 0) {
            $backups[] = [
                'name' => $item,
                'path' => $itemPath,
                'time' => filemtime($itemPath)
            ];
        }
    }
    
    // 按时间排序（最新的在前）
    usort($backups, function($a, $b) {
        return $b['time'] - $a['time'];
    });
    
    // 删除多余的备份
    $deletedCount = 0;
    for ($i = $keepCount; $i < count($backups); $i++) {
        $backupPath = $backups[$i]['path'];
        if (removeDirectory($backupPath)) {
            logMessage("删除旧备份: " . $backups[$i]['name']);
            $deletedCount++;
        }
    }
    
    if ($deletedCount > 0) {
        logMessage("清理完成: 删除了 $deletedCount 个旧备份");
    }
}

// 删除目录
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    try {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getPathname());
            } else {
                unlink($file->getPathname());
            }
        }
        
        return rmdir($dir);
    } catch (Exception $e) {
        logMessage("删除目录失败: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

// 主备份流程
try {
    logMessage("开始备份流程 (类型: $backupType)");
    
    // 1. 创建备份目录
    list($backupName, $backupPath) = createBackupDirectory($config);
    logMessage("创建备份目录: $backupName");
    
    $stats = [];
    
    // 2. 备份数据库
    try {
        $databaseFile = backupDatabase($config, $backupPath);
        $stats['database_file'] = $databaseFile;
        $stats['database_size'] = filesize($databaseFile);
    } catch (Exception $e) {
        logMessage("数据库备份失败: " . $e->getMessage(), 'ERROR');
        // 继续备份文件，即使数据库备份失败
    }
    
    // 3. 备份文件
    $stats['files_count'] = backupFiles($config, $backupPath, $backupType);
    
    // 4. 计算总大小
    $stats['total_size'] = calculateDirectorySize($backupPath);
    
    // 5. 创建备份信息
    $backupInfo = createBackupInfo($config, $backupPath, $backupName, $backupType, $stats);
    
    // 6. 清理旧备份
    if ($backupType === 'full') {
        cleanupOldBackups($config, 5); // 保留5个完整备份
    } else {
        cleanupOldBackups($config, 10); // 保留10个快速备份
    }
    
    logMessage("备份完成!");
    logMessage("备份名称: $backupName");
    logMessage("备份路径: $backupPath");
    logMessage("总大小: " . formatBytes($stats['total_size']));
    
    // 输出JSON结果（供API调用）
    if (php_sapi_name() !== 'cli') {
        echo json_encode([
            'success' => true,
            'backup_name' => $backupName,
            'backup_path' => $backupPath,
            'stats' => $stats
        ]);
    }
    
} catch (Exception $e) {
    logMessage("备份失败: " . $e->getMessage(), 'ERROR');
    
    // 清理失败的备份目录
    if (isset($backupPath) && is_dir($backupPath)) {
        removeDirectory($backupPath);
        logMessage("清理失败的备份目录: $backupPath");
    }
    
    if (php_sapi_name() !== 'cli') {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    
    exit(1);
}
?> 