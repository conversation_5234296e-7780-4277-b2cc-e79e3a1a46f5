import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api/auth'
import { storage } from '@/utils/storage'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(storage.get('token'))
  const user = ref(storage.get('user'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userName = computed(() => user.value?.username || '')

  // 动作
  async function login(credentials) {
    isLoading.value = true
    try {
      const response = await authAPI.login(credentials)
      
      if (response.success) {
        token.value = response.data.token
        user.value = response.data.user
        
        // 持久化存储
        storage.set('token', token.value)
        storage.set('user', user.value)
        
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: '登录失败，请稍后重试' }
    } finally {
      isLoading.value = false
    }
  }

  async function register(userData) {
    isLoading.value = true
    try {
      const response = await authAPI.register(userData)
      
      if (response.success) {
        return { success: true, message: '注册成功，请登录' }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Register error:', error)
      return { success: false, message: '注册失败，请稍后重试' }
    } finally {
      isLoading.value = false
    }
  }

  async function logout() {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = null
      user.value = null
      storage.remove('token')
      storage.remove('user')
      storage.remove('lastSelectedBookId')
    }
  }

  async function refreshUserInfo() {
    if (!token.value) return

    try {
      const response = await authAPI.getProfile()
      if (response.success) {
        user.value = response.data
        storage.set('user', user.value)
      }
    } catch (error) {
      console.error('Refresh user info error:', error)
      // 如果获取用户信息失败，可能token已过期
      if (error.response?.status === 401) {
        await logout()
      }
    }
  }

  // 初始化时检查token有效性
  function initialize() {
    if (token.value) {
      refreshUserInfo()
    }
  }

  return {
    // 状态
    token,
    user,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userName,
    
    // 动作
    login,
    register,
    logout,
    refreshUserInfo,
    initialize
  }
})
