<?php
/**
 * 备份下载脚本
 * 提供安全的备份文件下载功能
 */

// 安全检查
session_start();

// 备份目录
$backupDir = __DIR__ . '/Backup';

// 获取备份名称
$backupName = $_GET['backup'] ?? null;

if (!$backupName) {
    http_response_code(400);
    die('错误：未指定备份名称');
}

// 安全检查：防止路径遍历攻击
if (strpos($backupName, '..') !== false || strpos($backupName, '/') !== false || strpos($backupName, '\\') !== false) {
    http_response_code(403);
    die('错误：无效的备份名称');
}

$backupPath = $backupDir . '/' . $backupName;

// 检查备份是否存在
if (!file_exists($backupPath)) {
    http_response_code(404);
    die('错误：备份不存在');
}

// 检查是否在允许的目录内
if (strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
    http_response_code(403);
    die('错误：访问被拒绝');
}

try {
    if (is_dir($backupPath)) {
        // 目录备份：创建临时ZIP文件
        downloadDirectoryAsZip($backupPath, $backupName);
    } elseif (is_file($backupPath) && pathinfo($backupPath, PATHINFO_EXTENSION) === 'zip') {
        // ZIP文件：直接下载
        downloadFile($backupPath, $backupName);
    } else {
        http_response_code(400);
        die('错误：不支持的备份格式');
    }
} catch (Exception $e) {
    http_response_code(500);
    die('错误：' . $e->getMessage());
}

/**
 * 下载文件
 */
function downloadFile($filePath, $fileName) {
    $fileSize = filesize($filePath);
    $mimeType = 'application/zip';
    
    // 设置下载头
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $fileName . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // 清理输出缓冲区
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // 分块读取文件，避免内存溢出
    $handle = fopen($filePath, 'rb');
    if ($handle === false) {
        throw new Exception('无法打开文件');
    }
    
    while (!feof($handle)) {
        $chunk = fread($handle, 8192); // 8KB chunks
        echo $chunk;
        flush();
    }
    
    fclose($handle);
}

/**
 * 将目录打包并下载
 */
function downloadDirectoryAsZip($dirPath, $dirName) {
    // 优先尝试使用tar命令
    if (downloadDirectoryAsTar($dirPath, $dirName)) {
        return;
    }
    
    // 如果tar不可用，检查是否有ZIP扩展
    if (!class_exists('ZipArchive')) {
        throw new Exception('系统不支持ZIP压缩，请联系管理员安装相关扩展');
    }
    
    // 使用ZIP方式
    downloadDirectoryAsZipFallback($dirPath, $dirName);
}

/**
 * 使用tar命令打包下载
 */
function downloadDirectoryAsTar($dirPath, $dirName) {
    // 检查tar命令是否可用
    $output = [];
    $returnCode = 0;
    exec('which tar 2>/dev/null', $output, $returnCode);
    
    if ($returnCode !== 0) {
        return false; // tar命令不可用
    }
    
    // 创建临时tar.gz文件
    $tempDir = sys_get_temp_dir();
    $tempTarPath = $tempDir . '/' . $dirName . '_' . time() . '.tar.gz';
    
    // 构建tar命令
    $command = sprintf(
        'cd %s && tar -czf %s %s 2>&1',
        escapeshellarg(dirname($dirPath)),
        escapeshellarg($tempTarPath),
        escapeshellarg(basename($dirPath))
    );
    
    exec($command, $output, $returnCode);
    
    if ($returnCode !== 0 || !file_exists($tempTarPath)) {
        // tar命令失败，清理可能的临时文件
        if (file_exists($tempTarPath)) {
            unlink($tempTarPath);
        }
        return false;
    }
    
    try {
        // 下载tar.gz文件
        downloadTarFile($tempTarPath, $dirName . '.tar.gz');
        return true;
    } finally {
        // 清理临时文件
        if (file_exists($tempTarPath)) {
            unlink($tempTarPath);
        }
    }
}

/**
 * 下载tar文件
 */
function downloadTarFile($filePath, $fileName) {
    $fileSize = filesize($filePath);
    
    // 设置下载头
    header('Content-Type: application/gzip');
    header('Content-Disposition: attachment; filename="' . $fileName . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // 清理输出缓冲区
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // 分块读取文件，避免内存溢出
    $handle = fopen($filePath, 'rb');
    if ($handle === false) {
        throw new Exception('无法打开文件');
    }
    
    while (!feof($handle)) {
        $chunk = fread($handle, 8192); // 8KB chunks
        echo $chunk;
        flush();
    }
    
    fclose($handle);
}

/**
 * ZIP方式备用方案
 */
function downloadDirectoryAsZipFallback($dirPath, $dirName) {
    // 创建临时ZIP文件
    $tempZipPath = sys_get_temp_dir() . '/' . $dirName . '_' . time() . '.zip';
    
    $zip = new ZipArchive();
    $result = $zip->open($tempZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
    
    if ($result !== TRUE) {
        throw new Exception('无法创建ZIP文件: ' . $result);
    }
    
    // 添加目录内容到ZIP
    addDirectoryToZip($zip, $dirPath, '');
    
    $zip->close();
    
    // 检查ZIP文件是否创建成功
    if (!file_exists($tempZipPath)) {
        throw new Exception('ZIP文件创建失败');
    }
    
    try {
        // 下载ZIP文件
        downloadFile($tempZipPath, $dirName . '.zip');
    } finally {
        // 清理临时文件
        if (file_exists($tempZipPath)) {
            unlink($tempZipPath);
        }
    }
}

/**
 * 递归添加目录到ZIP
 */
function addDirectoryToZip($zip, $source, $prefix) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $file) {
        $relativePath = $prefix . substr($file->getPathname(), strlen($source) + 1);
        
        if ($file->isDir()) {
            // 添加目录
            $zip->addEmptyDir($relativePath);
        } elseif ($file->isFile()) {
            // 添加文件
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }
}

/**
 * 记录下载日志
 */
function logDownload($backupName, $userIP) {
    $logFile = __DIR__ . '/logs/download.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = sprintf(
        "[%s] IP: %s - 下载备份: %s\n",
        date('Y-m-d H:i:s'),
        $userIP,
        $backupName
    );
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 记录下载日志
$userIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
logDownload($backupName, $userIP);
?> 