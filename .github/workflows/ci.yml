name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  # PHP后端测试
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_accounting
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_pass
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql
        coverage: xdebug
    
    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-
    
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
    
    - name: Setup test database
      run: |
        mysql -h 127.0.0.1 -u test_user -ptest_pass test_accounting < backend/database/schema.sql
    
    - name: Run PHP tests
      run: |
        vendor/bin/phpunit --configuration tests/phpunit.xml --coverage-clover coverage.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: backend
        name: backend-coverage
    
    - name: Run PHP linting
      run: |
        find backend -name "*.php" -exec php -l {} \;
    
    - name: Security check
      run: |
        composer audit

  # 前端测试
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: vue-app/package-lock.json
    
    - name: Install dependencies
      working-directory: vue-app
      run: npm ci
    
    - name: Run linting
      working-directory: vue-app
      run: npm run lint
    
    - name: Run type checking
      working-directory: vue-app
      run: npm run type-check
    
    - name: Run unit tests
      working-directory: vue-app
      run: npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./vue-app/coverage/lcov.info
        flags: frontend
        name: frontend-coverage
    
    - name: Build application
      working-directory: vue-app
      run: npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: vue-app/dist

  # 端到端测试
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_accounting
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_pass
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install PHP dependencies
      run: composer install --prefer-dist --no-progress
    
    - name: Setup test database
      run: |
        mysql -h 127.0.0.1 -u test_user -ptest_pass test_accounting < backend/database/schema.sql
    
    - name: Start PHP server
      run: |
        php -S localhost:8000 -t . &
        sleep 5
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Run E2E tests
      run: npx playwright test
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Download frontend build
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: dist
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 这里添加实际的部署脚本
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # 这里添加冒烟测试脚本

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Download frontend build
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: dist
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的部署脚本
    
    - name: Run health checks
      run: |
        echo "Running health checks..."
        # 这里添加健康检查脚本
    
    - name: Notify deployment
      run: |
        echo "Notifying team about deployment..."
        # 这里添加通知脚本（Slack、邮件等）
