<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备份管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .action-buttons .btn {
            width: 100%;
            text-align: center;
            white-space: nowrap;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .backup-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .backup-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .backup-list-header h3 {
            margin: 0;
            color: #333;
        }

        .backup-list-actions {
            display: flex;
            gap: 10px;
        }

        .backup-list-actions .btn-small {
            white-space: nowrap;
        }

        .backup-item {
            background: white;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .backup-item:last-child {
            margin-bottom: 0;
        }

        .backup-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .backup-checkbox {
            width: auto !important;
            margin: 0;
        }

        .backup-name {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .backup-type-badge {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: normal;
        }

        .backup-details {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 13px;
            color: #666;
        }

        .backup-info {
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .backup-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        /* 恢复确认对话框 */
        .restore-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .restore-modal.show {
            display: flex;
        }

        .restore-dialog {
            background: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .restore-dialog h3 {
            color: #e74c3c;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .restore-dialog p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .restore-options {
            margin: 20px 0;
        }

        .restore-option {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .restore-option:hover {
            background: #f8f9fa;
            border-color: #667eea;
        }

        .restore-option input[type="radio"] {
            margin-right: 10px;
        }

        .restore-option label {
            cursor: pointer;
            flex: 1;
        }

        .restore-option .option-desc {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        /* 响应式设计优化 */
        @media (max-width: 1024px) {
            .container {
                max-width: 95%;
                margin: 10px auto;
            }
            
            .content {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .action-buttons {
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
            }
            
            .backup-details {
                gap: 15px;
                font-size: 12px;
            }
            
            .backup-actions {
                gap: 8px;
            }
            
            .backup-list-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .backup-list-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }

        @media (max-width: 480px) {
            .action-buttons {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
            
            .backup-details {
                flex-direction: column;
                gap: 8px;
            }
            
            .backup-actions {
                justify-content: center;
            }
            
            .backup-list-actions {
                flex-direction: column;
                gap: 8px;
            }
            
            .backup-list-actions .btn-small {
                width: 100%;
                text-align: center;
            }
        }

        @media (max-width: 360px) {
            .action-buttons {
                grid-template-columns: 1fr;
                gap: 8px;
            }
            
            .action-buttons .btn {
                font-size: 13px;
                padding: 10px 15px;
            }
        }

        /* 高级选项面板 */
        .advanced-panel {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .advanced-panel h3 {
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 18px;
        }

        .advanced-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .option-group {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .option-group:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .option-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .option-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            transition: border-color 0.3s ease;
        }

        .option-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .option-group input[type="checkbox"] {
            width: auto !important;
            margin-right: 8px;
            transform: scale(1.1);
            accent-color: #667eea;
        }

        .option-group label input[type="checkbox"] {
            margin-bottom: 0;
        }

        /* 批量操作面板 */
        .batch-panel {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .batch-panel h3 {
            margin-bottom: 15px;
            color: #856404;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        .batch-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .batch-controls button {
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .batch-controls button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            background: #6c757d !important;
        }

        .batch-controls button:not(:disabled):hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 备份管理器</h1>
            <p>网站和数据库备份管理工具</p>
        </div>

        <div class="content">
            <div id="status" class="status"></div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="createBackup()">
                    📦 创建新备份
                </button>
                <button class="btn btn-success" onclick="refreshBackupList()">
                    🔄 刷新列表
                </button>
                <button class="btn btn-warning" onclick="showAdvancedOptions()">
                    ⚙️ 高级选项
                </button>
                <a href="index.html" class="btn btn-warning">
                    🏠 返回主页
                </a>
            </div>

            <!-- 高级选项面板 -->
            <div id="advanced-panel" class="advanced-panel" style="display: none;">
                <h3>🔧 高级备份选项</h3>
                <div class="advanced-options">
                    <div class="option-group">
                        <label>备份类型：</label>
                        <select id="backup-type">
                            <option value="quick">快速备份（仅核心文件）</option>
                            <option value="full">完整备份（所有文件）</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label>
                            <input type="checkbox" id="auto-cleanup" checked>
                            自动清理旧备份（保留最新10个）
                        </label>
                    </div>
                    <div class="option-group">
                        <label>
                            <input type="checkbox" id="compress-backup">
                            压缩备份为ZIP文件
                        </label>
                    </div>
                </div>
            </div>

            <!-- 批量操作面板 -->
            <div id="batch-panel" class="batch-panel" style="display: none;">
                <h3>📋 批量操作</h3>
                <div class="batch-controls">
                    <button class="btn btn-primary btn-small" onclick="selectAllBackups()">
                        ✅ 全选
                    </button>
                    <button class="btn btn-warning btn-small" onclick="clearSelection()">
                        ❌ 清除选择
                    </button>
                    <button class="btn btn-warning btn-small" onclick="deleteSelectedBackups()">
                        🗑️ 删除选中
                    </button>
                    <button class="btn btn-success btn-small" onclick="exportSelectedBackups()">
                        📤 导出选中
                    </button>
                </div>
            </div>

            <div class="backup-list">
                <div class="backup-list-header">
                    <h3>📋 备份列表</h3>
                    <div class="backup-list-actions">
                        <button class="btn btn-primary btn-small" onclick="validateAllBackups()">
                            🔍 验证所有备份
                        </button>
                        <button class="btn btn-warning btn-small" onclick="showBatchOperations()">
                            📋 批量操作
                        </button>
                    </div>
                </div>
                <div id="backup-list-content" class="loading">
                    正在加载备份列表...
                </div>
            </div>
        </div>
    </div>

    <!-- 恢复确认对话框 -->
    <div id="restore-modal" class="restore-modal">
        <div class="restore-dialog">
            <h3>⚠️ 数据恢复确认</h3>
            <p>您即将恢复备份：<strong id="restore-backup-name"></strong></p>
            <p style="color: #e74c3c; font-weight: 600;">
                ⚠️ 警告：此操作将完全覆盖当前的数据库和网站文件！请确保您了解此操作的后果。
            </p>

            <div class="restore-options">
                <div class="restore-option">
                    <input type="radio" id="restore-full" name="restore-type" value="full" checked>
                    <label for="restore-full">
                        <strong>🔄 完整恢复</strong>
                        <div class="option-desc">恢复数据库和所有网站文件（推荐）</div>
                    </label>
                </div>
                <div class="restore-option">
                    <input type="radio" id="restore-database" name="restore-type" value="database">
                    <label for="restore-database">
                        <strong>🗄️ 仅恢复数据库</strong>
                        <div class="option-desc">只恢复数据库，保留当前网站文件</div>
                    </label>
                </div>
                <div class="restore-option">
                    <input type="radio" id="restore-files" name="restore-type" value="files">
                    <label for="restore-files">
                        <strong>📁 仅恢复文件</strong>
                        <div class="option-desc">只恢复网站文件，保留当前数据库</div>
                    </label>
                </div>
            </div>

            <p style="font-size: 14px; color: #666; background: #f8f9fa; padding: 10px; border-radius: 6px;">
                💡 提示：恢复前会自动创建当前状态的备份，以防需要回滚。
            </p>

            <div class="modal-actions">
                <button class="btn btn-warning" onclick="closeRestoreModal()">
                    ❌ 取消
                </button>
                <button class="btn btn-primary" onclick="confirmRestore()">
                    ✅ 确认恢复
                </button>
            </div>
        </div>
    </div>

    <script>
        // 显示状态消息
        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';

            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        // 创建备份
        async function createBackup() {
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '⏳ 创建中...';
            btn.disabled = true;

            try {
                // 获取高级选项
                const backupType = document.getElementById('backup-type')?.value || 'quick';
                const autoCleanup = document.getElementById('auto-cleanup')?.checked || true;
                const compressBackup = document.getElementById('compress-backup')?.checked || false;

                const response = await fetch('backup_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        action: 'create_backup',
                        backup_type: backupType,
                        auto_cleanup: autoCleanup,
                        compress: compressBackup
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus('✅ 备份创建成功！', 'success');
                    refreshBackupList();
                } else {
                    showStatus('❌ 备份创建失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, 'error');
            } finally {
                btn.textContent = originalText;
                btn.disabled = false;
            }
        }

        // 刷新备份列表
        async function refreshBackupList() {
            const listEl = document.getElementById('backup-list-content');
            listEl.innerHTML = '<div class="loading">正在加载备份列表...</div>';

            try {
                const response = await fetch('backup_api.php?action=list_backups');
                const result = await response.json();

                if (result.success) {
                    displayBackupList(result.data);
                } else {
                    listEl.innerHTML = '<div class="loading">加载失败: ' + result.message + '</div>';
                }
            } catch (error) {
                listEl.innerHTML = '<div class="loading">网络错误: ' + error.message + '</div>';
            }
        }

        // 显示备份列表
        function displayBackupList(backups) {
            const listEl = document.getElementById('backup-list-content');

            if (backups.length === 0) {
                listEl.innerHTML = '<div class="loading">暂无备份文件</div>';
                return;
            }

            let html = '';
            backups.forEach(backup => {
                const backupTypeIcon = backup.type === 'zip' ? '📦' : '📁';
                const backupTypeText = backup.type === 'zip' ? 'ZIP压缩包' : '目录备份';
                
                // 备份类型标识
                const backupModeIcon = getBackupModeIcon(backup.backup_type);
                const backupModeText = getBackupModeText(backup.backup_type);
                const backupModeColor = getBackupModeColor(backup.backup_type);
                
                html += `
                    <div class="backup-item">
                        <div class="backup-header">
                            <input type="checkbox" class="backup-checkbox" 
                                   data-backup-name="${backup.name}" 
                                   onchange="updateBatchControls()">
                            <div class="backup-name">
                                ${backupTypeIcon} ${backup.name}
                                <span class="backup-type-badge">${backupTypeText}</span>
                                <span class="backup-type-badge" style="background: ${backupModeColor};">
                                    ${backupModeIcon} ${backupModeText}
                                </span>
                            </div>
                        </div>
                        <div class="backup-info">
                            <div class="backup-details">
                                <span>📅 ${backup.date}</span>
                                <span style="margin-left: 15px;">📦 ${backup.size}</span>
                                <span style="margin-left: 15px;">🏷️ ${backupTypeText}</span>
                                <span style="margin-left: 15px;">⚡ ${backupModeText}备份</span>
                            </div>
                            <div class="backup-actions">
                                <button class="btn btn-primary btn-small" onclick="restoreBackup('${backup.name}')">
                                    🔄 恢复
                                </button>
                                <button class="btn btn-success btn-small" onclick="downloadBackup('${backup.name}')">
                                    ⬇️ 下载
                                </button>
                                <button class="btn btn-warning btn-small" onclick="validateBackup('${backup.name}')">
                                    🔍 验证
                                </button>
                                <button class="btn btn-warning btn-small" onclick="deleteBackup('${backup.name}')">
                                    🗑️ 删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });

            listEl.innerHTML = html;
            updateBatchControls();
        }

        // 获取备份模式图标
        function getBackupModeIcon(backupType) {
            switch (backupType) {
                case 'quick': return '⚡';
                case 'full': return '🔄';
                default: return '❓';
            }
        }

        // 获取备份模式文本
        function getBackupModeText(backupType) {
            switch (backupType) {
                case 'quick': return '快速';
                case 'full': return '完整';
                default: return '未知';
            }
        }

        // 获取备份模式颜色
        function getBackupModeColor(backupType) {
            switch (backupType) {
                case 'quick': return '#28a745'; // 绿色
                case 'full': return '#007bff';  // 蓝色
                default: return '#6c757d';      // 灰色
            }
        }

        // 验证单个备份
        async function validateBackup(backupName) {
            showStatus('🔍 正在验证备份: ' + backupName, 'info');
            
            try {
                const response = await fetch('backup_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'validate_backup',
                        backup_name: backupName
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    if (result.data.is_valid) {
                        showStatus(`✅ 备份 "${backupName}" 验证通过`, 'success');
                    } else {
                        showStatus(`❌ 备份 "${backupName}" 验证失败: ${result.data.error}`, 'error');
                    }
                } else {
                    showStatus('❌ 验证失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('❌ 验证错误: ' + error.message, 'error');
            }
        }

        // 下载备份
        function downloadBackup(backupName) {
            // 使用专门的下载脚本，支持多种压缩格式
            window.open(`download_backup.php?backup=${encodeURIComponent(backupName)}`, '_blank');
        }

        // 恢复备份
        let currentRestoreBackup = null;

        function restoreBackup(backupName) {
            currentRestoreBackup = backupName;
            document.getElementById('restore-backup-name').textContent = backupName;
            document.getElementById('restore-modal').classList.add('show');
        }

        function closeRestoreModal() {
            document.getElementById('restore-modal').classList.remove('show');
            currentRestoreBackup = null;
        }

        async function confirmRestore() {
            if (!currentRestoreBackup) return;

            const restoreType = document.querySelector('input[name="restore-type"]:checked').value;
            const modal = document.getElementById('restore-modal');
            const confirmBtn = modal.querySelector('.btn-primary');
            const originalText = confirmBtn.textContent;

            confirmBtn.textContent = '⏳ 恢复中...';
            confirmBtn.disabled = true;

            try {
                const response = await fetch('backup_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'restore_backup',
                        backup_name: currentRestoreBackup,
                        restore_type: restoreType
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(`✅ ${getRestoreTypeText(restoreType)}恢复成功！`, 'success');
                    closeRestoreModal();

                    // 如果是完整恢复，提示用户刷新页面
                    if (restoreType === 'full') {
                        setTimeout(() => {
                            if (confirm('恢复完成！是否刷新页面以查看恢复后的网站？')) {
                                window.location.reload();
                            }
                        }, 2000);
                    }
                } else {
                    showStatus('❌ 恢复失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, 'error');
            } finally {
                confirmBtn.textContent = originalText;
                confirmBtn.disabled = false;
            }
        }

        function getRestoreTypeText(type) {
            switch (type) {
                case 'full': return '完整';
                case 'database': return '数据库';
                case 'files': return '文件';
                default: return '';
            }
        }

        // 删除备份
        async function deleteBackup(backupName) {
            if (!confirm(`确定要删除备份 "${backupName}" 吗？此操作不可恢复！`)) {
                return;
            }

            // 显示删除中状态
            showStatus('🗑️ 正在删除备份...', 'info');

            try {
                const response = await fetch('backup_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete_backup',
                        backup_name: backupName
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus('✅ 备份删除成功！', 'success');
                    refreshBackupList();
                } else {
                    showStatus('❌ 删除失败: ' + result.message, 'error');
                    console.error('删除失败详情:', result);
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, 'error');
                console.error('删除备份网络错误:', error);
            }
        }

        // 显示高级选项
        function showAdvancedOptions() {
            const panel = document.getElementById('advanced-panel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 显示批量操作
        function showBatchOperations() {
            const panel = document.getElementById('batch-panel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 验证所有备份
        async function validateAllBackups() {
            showStatus('🔍 正在验证备份...', 'info');
            
            try {
                const response = await fetch('backup_api.php?action=validate_backups');
                const result = await response.json();
                
                if (result.success) {
                    const validCount = result.data.valid_count;
                    const totalCount = result.data.total_count;
                    showStatus(`✅ 验证完成: ${validCount}/${totalCount} 个备份有效`, 'success');
                } else {
                    showStatus('❌ 验证失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('❌ 验证错误: ' + error.message, 'error');
            }
        }

        // 全选备份
        function selectAllBackups() {
            const checkboxes = document.querySelectorAll('.backup-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            updateBatchControls();
        }

        // 清除选择
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.backup-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            updateBatchControls();
        }

        // 更新批量控制按钮状态
        function updateBatchControls() {
            const selectedCount = document.querySelectorAll('.backup-checkbox:checked').length;
            const batchButtons = document.querySelectorAll('#batch-panel button');
            
            batchButtons.forEach(btn => {
                if (btn.textContent.includes('删除选中') || btn.textContent.includes('导出选中')) {
                    btn.disabled = selectedCount === 0;
                }
            });
        }

        // 删除选中的备份
        async function deleteSelectedBackups() {
            const selectedBackups = Array.from(document.querySelectorAll('.backup-checkbox:checked'))
                .map(cb => cb.dataset.backupName);
            
            if (selectedBackups.length === 0) {
                showStatus('⚠️ 请先选择要删除的备份', 'error');
                return;
            }
            
            if (!confirm(`确定要删除 ${selectedBackups.length} 个备份吗？此操作不可恢复！`)) {
                return;
            }
            
            showStatus('🗑️ 正在删除选中的备份...', 'info');
            
            let successCount = 0;
            let failCount = 0;
            
            for (const backupName of selectedBackups) {
                try {
                    const response = await fetch('backup_api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'delete_backup',
                            backup_name: backupName
                        })
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                }
            }
            
            showStatus(`✅ 批量删除完成: 成功 ${successCount} 个, 失败 ${failCount} 个`, 
                      failCount > 0 ? 'error' : 'success');
            
            refreshBackupList();
            clearSelection();
        }

        // 导出选中的备份
        async function exportSelectedBackups() {
            const selectedBackups = Array.from(document.querySelectorAll('.backup-checkbox:checked'))
                .map(cb => cb.dataset.backupName);
            
            if (selectedBackups.length === 0) {
                showStatus('⚠️ 请先选择要导出的备份', 'error');
                return;
            }
            
            showStatus('📤 正在准备导出...', 'info');
            
            // 逐个下载选中的备份
            for (const backupName of selectedBackups) {
                setTimeout(() => {
                    downloadBackup(backupName);
                }, 1000); // 延迟1秒避免同时下载太多文件
            }
            
            showStatus(`✅ 开始下载 ${selectedBackups.length} 个备份文件`, 'success');
        }

        // 页面加载时获取备份列表
        document.addEventListener('DOMContentLoaded', function() {
            refreshBackupList();
        });
    </script>
</body>
</html>
