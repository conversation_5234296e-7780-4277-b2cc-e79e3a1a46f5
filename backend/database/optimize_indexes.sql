-- 数据库索引优化脚本
-- 执行前请备份数据库

-- 1. 删除重复索引
-- 检查是否存在重复索引并删除
DROP INDEX IF EXISTS idx_users_email ON users;
DROP INDEX IF EXISTS idx_books_user ON account_books;

-- 2. 添加复合索引优化查询性能

-- 优化账本记录查询
CREATE INDEX IF NOT EXISTS idx_records_book_completed ON records(account_book_id, is_completed);
CREATE INDEX IF NOT EXISTS idx_records_book_date ON records(account_book_id, date);
CREATE INDEX IF NOT EXISTS idx_records_book_decreasing ON records(account_book_id, is_decreasing);

-- 优化统计查询
CREATE INDEX IF NOT EXISTS idx_records_completed_month ON records(is_completed, completed_month);
CREATE INDEX IF NOT EXISTS idx_records_renewal_time ON records(renewal_time);

-- 优化月度状态查询
CREATE INDEX IF NOT EXISTS idx_monthly_states_month ON record_monthly_states(view_month, is_completed);
CREATE INDEX IF NOT EXISTS idx_monthly_states_record_month ON record_monthly_states(record_id, view_month);

-- 优化用户相关查询
CREATE INDEX IF NOT EXISTS idx_account_books_user_recycle ON account_books(user_id, is_recycle_bin);
CREATE INDEX IF NOT EXISTS idx_account_books_user_created ON account_books(user_id, created_at);

-- 优化时间范围查询
CREATE INDEX IF NOT EXISTS idx_records_updated_at ON records(updated_at);
CREATE INDEX IF NOT EXISTS idx_records_created_at ON records(created_at);

-- 3. 分析表以更新统计信息
ANALYZE TABLE users;
ANALYZE TABLE account_books;
ANALYZE TABLE records;
ANALYZE TABLE record_monthly_states;

-- 4. 显示索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE()
ORDER BY TABLE_NAME, INDEX_NAME;
