<?php

/**
 * 统一参数验证类
 */
class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
        $this->errors = [];
    }
    
    /**
     * 验证必填字段
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || $this->data[$field] === '' || $this->data[$field] === null) {
            $this->errors[] = $message ?? "字段 {$field} 不能为空";
        }
        return $this;
    }
    
    /**
     * 验证字符串长度
     */
    public function length($field, $min = null, $max = null, $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        $length = mb_strlen($this->data[$field], 'UTF-8');
        
        if ($min !== null && $length < $min) {
            $this->errors[] = $message ?? "字段 {$field} 长度不能少于 {$min} 个字符";
        }
        
        if ($max !== null && $length > $max) {
            $this->errors[] = $message ?? "字段 {$field} 长度不能超过 {$max} 个字符";
        }
        
        return $this;
    }
    
    /**
     * 验证数字
     */
    public function numeric($field, $min = null, $max = null, $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        if (!is_numeric($this->data[$field])) {
            $this->errors[] = $message ?? "字段 {$field} 必须是数字";
            return $this;
        }
        
        $value = floatval($this->data[$field]);
        
        if ($min !== null && $value < $min) {
            $this->errors[] = $message ?? "字段 {$field} 不能小于 {$min}";
        }
        
        if ($max !== null && $value > $max) {
            $this->errors[] = $message ?? "字段 {$field} 不能大于 {$max}";
        }
        
        return $this;
    }
    
    /**
     * 验证邮箱格式
     */
    public function email($field, $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
            $this->errors[] = $message ?? "字段 {$field} 邮箱格式不正确";
        }
        
        return $this;
    }
    
    /**
     * 验证日期格式
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        $date = DateTime::createFromFormat($format, $this->data[$field]);
        if (!$date || $date->format($format) !== $this->data[$field]) {
            $this->errors[] = $message ?? "字段 {$field} 日期格式不正确，应为 {$format}";
        }
        
        return $this;
    }
    
    /**
     * 验证枚举值
     */
    public function in($field, $values, $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        if (!in_array($this->data[$field], $values)) {
            $allowedValues = implode(', ', $values);
            $this->errors[] = $message ?? "字段 {$field} 值无效，允许的值: {$allowedValues}";
        }
        
        return $this;
    }
    
    /**
     * 验证正则表达式
     */
    public function regex($field, $pattern, $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        if (!preg_match($pattern, $this->data[$field])) {
            $this->errors[] = $message ?? "字段 {$field} 格式不正确";
        }
        
        return $this;
    }
    
    /**
     * 自定义验证
     */
    public function custom($field, $callback, $message = null) {
        if (!isset($this->data[$field])) return $this;
        
        if (!$callback($this->data[$field])) {
            $this->errors[] = $message ?? "字段 {$field} 验证失败";
        }
        
        return $this;
    }
    
    /**
     * 检查是否有错误
     */
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    /**
     * 获取所有错误
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * 获取第一个错误
     */
    public function getFirstError() {
        return $this->errors[0] ?? null;
    }
    
    /**
     * 验证记录数据
     */
    public static function validateRecord($data) {
        $validator = new self($data);
        
        $validator
            ->required('name', '姓名不能为空')
            ->length('name', 1, 50, '姓名长度必须在1-50个字符之间')
            ->required('amount', '金额不能为空')
            ->numeric('amount', 0.01, 999999.99, '金额必须在0.01-999999.99之间')
            ->numeric('monthly_amount', 0, 999999.99, '每月金额必须在0-999999.99之间')
            ->numeric('renewal_amount', 0, 999999.99, '续期金额必须在0-999999.99之间')
            ->required('date', '日期不能为空')
            ->date('date', 'Y-m-d', '日期格式不正确')
            ->required('renewal_time', '续期时间不能为空')
            ->in('renewal_time', ['一个月', '二个月', '三个月', '六个月', '永久'], '续期时间选项无效')
            ->length('remark', 0, 200, '备注长度不能超过200个字符');
        
        return $validator;
    }
    
    /**
     * 验证账本数据
     */
    public static function validateAccountBook($data) {
        $validator = new self($data);
        
        $validator
            ->required('name', '账本名称不能为空')
            ->length('name', 1, 50, '账本名称长度必须在1-50个字符之间')
            ->length('description', 0, 200, '账本描述长度不能超过200个字符');
        
        return $validator;
    }
    
    /**
     * 验证用户注册数据
     */
    public static function validateUserRegistration($data) {
        $validator = new self($data);
        
        $validator
            ->required('username', '用户名不能为空')
            ->length('username', 3, 20, '用户名长度必须在3-20个字符之间')
            ->regex('username', '/^[a-zA-Z0-9_]+$/', '用户名只能包含字母、数字和下划线')
            ->required('email', '邮箱不能为空')
            ->email('email', '邮箱格式不正确')
            ->required('password', '密码不能为空')
            ->length('password', 6, 50, '密码长度必须在6-50个字符之间');
        
        return $validator;
    }
    
    /**
     * 验证分页参数
     */
    public static function validatePagination($data) {
        $validator = new self($data);
        
        $validator
            ->numeric('page', 1, 1000, '页码必须在1-1000之间')
            ->numeric('limit', 1, 100, '每页数量必须在1-100之间');
        
        return $validator;
    }
}
?>
