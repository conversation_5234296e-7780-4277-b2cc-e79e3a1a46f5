<?php

/**
 * 统一日志管理类
 */
class Logger {
    private static $logDir = __DIR__ . '/../logs/';
    
    // 日志级别
    const LEVEL_DEBUG = 'DEBUG';
    const LEVEL_INFO = 'INFO';
    const LEVEL_WARNING = 'WARNING';
    const LEVEL_ERROR = 'ERROR';
    const LEVEL_CRITICAL = 'CRITICAL';
    
    /**
     * 初始化日志目录
     */
    private static function initLogDir() {
        if (!is_dir(self::$logDir)) {
            mkdir(self::$logDir, 0755, true);
        }
    }
    
    /**
     * 写入日志
     */
    private static function writeLog($level, $message, $context = []) {
        self::initLogDir();
        
        $timestamp = date('Y-m-d H:i:s');
        $userId = $_SESSION['user_id'] ?? 'anonymous';
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        // 构建日志条目
        $logEntry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'user_id' => $userId,
            'ip' => $ip,
            'user_agent' => $userAgent,
            'request_uri' => $requestUri,
            'context' => $context
        ];
        
        // 格式化日志内容
        $logLine = sprintf(
            "[%s] %s: %s | User: %s | IP: %s | URI: %s",
            $timestamp,
            $level,
            $message,
            $userId,
            $ip,
            $requestUri
        );
        
        if (!empty($context)) {
            $logLine .= " | Context: " . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        
        $logLine .= PHP_EOL;
        
        // 写入日志文件
        $logFile = self::$logDir . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // 错误级别的日志同时写入错误日志
        if (in_array($level, [self::LEVEL_ERROR, self::LEVEL_CRITICAL])) {
            $errorLogFile = self::$logDir . 'error.log';
            file_put_contents($errorLogFile, $logLine, FILE_APPEND | LOCK_EX);
        }
        
        // 在开发环境中同时输出到PHP错误日志
        if (($_ENV['APP_ENV'] ?? 'development') === 'development') {
            error_log($logLine);
        }
    }
    
    /**
     * 记录调试信息
     */
    public static function debug($message, $context = []) {
        self::writeLog(self::LEVEL_DEBUG, $message, $context);
    }
    
    /**
     * 记录一般信息
     */
    public static function info($message, $context = []) {
        self::writeLog(self::LEVEL_INFO, $message, $context);
    }
    
    /**
     * 记录警告信息
     */
    public static function warning($message, $context = []) {
        self::writeLog(self::LEVEL_WARNING, $message, $context);
    }
    
    /**
     * 记录错误信息
     */
    public static function error($message, $context = []) {
        self::writeLog(self::LEVEL_ERROR, $message, $context);
    }
    
    /**
     * 记录严重错误
     */
    public static function critical($message, $context = []) {
        self::writeLog(self::LEVEL_CRITICAL, $message, $context);
    }
    
    /**
     * 记录API请求
     */
    public static function logApiRequest($method, $endpoint, $params = [], $response = null) {
        $context = [
            'method' => $method,
            'endpoint' => $endpoint,
            'params' => $params,
            'response_status' => $response ? 'success' : 'error'
        ];
        
        self::info("API Request: $method $endpoint", $context);
    }
    
    /**
     * 记录数据库操作
     */
    public static function logDatabaseOperation($operation, $table, $data = []) {
        $context = [
            'operation' => $operation,
            'table' => $table,
            'data' => $data
        ];
        
        self::info("Database Operation: $operation on $table", $context);
    }
    
    /**
     * 记录用户操作
     */
    public static function logUserAction($action, $details = []) {
        $context = [
            'action' => $action,
            'details' => $details
        ];
        
        self::info("User Action: $action", $context);
    }
    
    /**
     * 记录安全事件
     */
    public static function logSecurityEvent($event, $details = []) {
        $context = [
            'security_event' => $event,
            'details' => $details
        ];
        
        self::warning("Security Event: $event", $context);
    }
    
    /**
     * 清理旧日志文件
     */
    public static function cleanOldLogs($daysToKeep = 30) {
        self::initLogDir();
        
        $files = glob(self::$logDir . '*.log');
        $cutoffTime = time() - ($daysToKeep * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
                self::info("Cleaned old log file: " . basename($file));
            }
        }
    }
    
    /**
     * 获取日志统计
     */
    public static function getLogStats($date = null) {
        $date = $date ?? date('Y-m-d');
        $logFile = self::$logDir . $date . '.log';
        
        if (!file_exists($logFile)) {
            return [
                'total' => 0,
                'by_level' => []
            ];
        }
        
        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        
        $stats = [
            'total' => 0,
            'by_level' => [
                self::LEVEL_DEBUG => 0,
                self::LEVEL_INFO => 0,
                self::LEVEL_WARNING => 0,
                self::LEVEL_ERROR => 0,
                self::LEVEL_CRITICAL => 0
            ]
        ];
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            $stats['total']++;
            
            foreach ($stats['by_level'] as $level => $count) {
                if (strpos($line, $level) !== false) {
                    $stats['by_level'][$level]++;
                    break;
                }
            }
        }
        
        return $stats;
    }
}
?>
