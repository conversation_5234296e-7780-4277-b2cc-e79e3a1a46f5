<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();

    switch ($method) {
        case 'GET':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                getRecords($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        case 'POST':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                createRecord($pdo, $user['user_id'], $matches[1]);
            } elseif (preg_match('/^\/(\d+)\/(\d+)\/toggle$/', $path, $matches)) {
                toggleRecord($pdo, $user['user_id'], $matches[1], $matches[2]);
            } elseif ($path === '/reset-monthly') {
                resetMonthlyRecords($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        case 'PUT':
            if (preg_match('/^\/(\d+)\/(\d+)$/', $path, $matches)) {
                updateRecord($pdo, $user['user_id'], $matches[1], $matches[2]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        case 'DELETE':
            if (preg_match('/^\/(\d+)\/(\d+)$/', $path, $matches)) {
                deleteRecord($pdo, $user['user_id'], $matches[1], $matches[2]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function getRecords($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }

    // 获取分页参数
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = max(1, min(100, (int)($_GET['limit'] ?? 20))); // 限制最大每页100条
    $month = $_GET['month'] ?? null;
    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $whereClause = "WHERE account_book_id = ?";
    $params = [$bookId];

    if ($month && preg_match('/^\d{4}-\d{2}$/', $month)) {
        $whereClause .= " AND DATE_FORMAT(date, '%Y-%m') = ?";
        $params[] = $month;
    }

    // 获取总数
    $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM records $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];

    // 获取分页数据
    $stmt = $pdo->prepare("
        SELECT * FROM records
        $whereClause
        ORDER BY date DESC, created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $records = $stmt->fetchAll();

    // 返回分页结果
    Response::success([
        'records' => $records,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total,
            'total_pages' => ceil($total / $limit),
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ]
    ]);
}

function createRecord($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }

    $data = getRequestData();

    // 验证必填字段（每月金额和续期金额改为可选）
    $required = ['date', 'name', 'amount', 'renewal_time'];
    foreach ($required as $field) {
        if (!isset($data[$field]) || $data[$field] === '') {
            Response::error("字段 {$field} 不能为空");
        }
    }

    // 每月金额和续期金额如果不填默认为0
    $data['monthly_amount'] = isset($data['monthly_amount']) && $data['monthly_amount'] !== '' ? $data['monthly_amount'] : '0';
    $data['renewal_amount'] = isset($data['renewal_amount']) && $data['renewal_amount'] !== '' ? $data['renewal_amount'] : '0';

    // 验证续期时间选项（增加一个月选项）
    $validRenewalTimes = ['一个月', '二个月', '三个月', '六个月', '永久'];
    if (!in_array($data['renewal_time'], $validRenewalTimes)) {
        Response::error('续期时间选项无效');
    }

    $stmt = $pdo->prepare("
        INSERT INTO records (
            account_book_id, date, name, amount, monthly_amount,
            renewal_time, renewal_amount, remark, accumulated_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0)
    ");

    $stmt->execute([
        $bookId,
        $data['date'],
        $data['name'],
        $data['amount'],
        $data['monthly_amount'],
        $data['renewal_time'],
        $data['renewal_amount'],
        $data['remark'] ?? ''
    ]);

    $recordId = $pdo->lastInsertId();

    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    Response::success($record, '记录创建成功');
}

function updateRecord($pdo, $userId, $bookId, $recordId) {
    // 验证权限
    $stmt = $pdo->prepare("
        SELECT r.* FROM records r
        JOIN account_books ab ON r.account_book_id = ab.id
        WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ?
    ");
    $stmt->execute([$recordId, $bookId, $userId]);
    $record = $stmt->fetch();

    if (!$record) {
        Response::error('记录不存在或无权限', 404);
    }

    $data = getRequestData();

    // 验证必填字段（每月金额和续期金额改为可选）
    $required = ['date', 'name', 'amount', 'renewal_time'];
    foreach ($required as $field) {
        if (!isset($data[$field]) || $data[$field] === '') {
            Response::error("字段 {$field} 不能为空");
        }
    }

    // 每月金额和续期金额如果不填默认为0
    $data['monthly_amount'] = isset($data['monthly_amount']) && $data['monthly_amount'] !== '' ? $data['monthly_amount'] : '0';
    $data['renewal_amount'] = isset($data['renewal_amount']) && $data['renewal_amount'] !== '' ? $data['renewal_amount'] : '0';

    $stmt = $pdo->prepare("
        UPDATE records SET
            date = ?, name = ?, amount = ?, monthly_amount = ?,
            renewal_time = ?, renewal_amount = ?, remark = ?
        WHERE id = ?
    ");

    $stmt->execute([
        $data['date'],
        $data['name'],
        $data['amount'],
        $data['monthly_amount'],
        $data['renewal_time'],
        $data['renewal_amount'],
        $data['remark'] ?? '',
        $recordId
    ]);

    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    Response::success($record, '记录更新成功');
}

function deleteRecord($pdo, $userId, $bookId, $recordId) {
    try {
        $pdo->beginTransaction();

        // 验证权限（确保不是回收站账本）
        $stmt = $pdo->prepare("
            SELECT r.*, ab.name as book_name
            FROM records r
            JOIN account_books ab ON r.account_book_id = ab.id
            WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ? AND ab.is_recycle_bin = 0
        ");
        $stmt->execute([$recordId, $bookId, $userId]);
        $record = $stmt->fetch();

        if (!$record) {
            Response::error('记录不存在或无权限', 404);
        }

        // 获取或创建回收站账本
        $recycleBin = getOrCreateRecycleBin($pdo, $userId);

        // 移动记录到回收站而不是删除
        $stmt = $pdo->prepare("
            UPDATE records SET
                account_book_id = ?,
                original_book_id = ?,
                deleted_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$recycleBin['id'], $bookId, $recordId]);

        $pdo->commit();

        Response::success([
            'record_id' => $recordId,
            'moved_to_recycle_bin' => true,
            'original_book' => $record['book_name']
        ], '记录已移动到回收站');

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        throw $e;
    }
}

/**
 * 获取或创建用户的回收站账本
 */
function getOrCreateRecycleBin($pdo, $userId) {
    // 先尝试获取现有的回收站账本
    $stmt = $pdo->prepare("
        SELECT * FROM account_books
        WHERE user_id = ? AND is_recycle_bin = 1
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $recycleBin = $stmt->fetch();

    if (!$recycleBin) {
        // 创建回收站账本
        $stmt = $pdo->prepare("
            INSERT INTO account_books (user_id, name, description, is_recycle_bin)
            VALUES (?, '🗑️ 回收站', '系统自动创建的回收站，用于存放已删除的记录', 1)
        ");
        $stmt->execute([$userId]);

        $recycleBinId = $pdo->lastInsertId();

        $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
        $stmt->execute([$recycleBinId]);
        $recycleBin = $stmt->fetch();
    }

    return $recycleBin;
}

function toggleRecord($pdo, $userId, $bookId, $recordId) {
    // 验证权限
    $stmt = $pdo->prepare("
        SELECT r.* FROM records r
        JOIN account_books ab ON r.account_book_id = ab.id
        WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ?
    ");
    $stmt->execute([$recordId, $bookId, $userId]);
    $record = $stmt->fetch();

    if (!$record) {
        Response::error('记录不存在或无权限', 404);
    }

    $currentMonth = date('Y-m');
    $newStatus = !$record['is_completed'];

    // 如果记录已被锁定且不是当月操作，不允许修改
    if ($record['is_locked'] && $record['completed_month'] !== $currentMonth) {
        Response::error('该记录已被月度锁定，无法修改');
    }

    // 计算新的累计金额
    $newAccumulatedAmount = $record['accumulated_amount'];
    if ($newStatus) {
        // 标记为完成
        $newAccumulatedAmount += $record['monthly_amount'];
        $completedMonth = $currentMonth;
    } else {
        // 取消完成
        if ($record['completed_month'] === $currentMonth) {
            // 当月操作，可以减少累计金额
            $newAccumulatedAmount -= $record['monthly_amount'];
        }
        $completedMonth = null;
    }

    $stmt = $pdo->prepare("
        UPDATE records SET
            is_completed = ?,
            accumulated_amount = ?,
            completed_month = ?
        WHERE id = ?
    ");
    $stmt->execute([$newStatus, $newAccumulatedAmount, $completedMonth, $recordId]);

    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    Response::success($record, '状态更新成功');
}

function resetMonthlyRecords($pdo, $userId) {
    // 获取用户的所有账本
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE user_id = ?");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();

    $bookIds = array_column($books, 'id');
    if (empty($bookIds)) {
        Response::success(null, '没有需要重置的记录');
    }

    $placeholders = str_repeat('?,', count($bookIds) - 1) . '?';

    // 锁定上个月的记录
    $lastMonth = date('Y-m', strtotime('-1 month'));
    $stmt = $pdo->prepare("
        UPDATE records SET is_locked = 1
        WHERE account_book_id IN ($placeholders)
        AND completed_month = ?
    ");
    $stmt->execute(array_merge($bookIds, [$lastMonth]));

    // 重置当月记录（取消勾选）
    $stmt = $pdo->prepare("
        UPDATE records SET
            is_completed = 0,
            completed_month = NULL
        WHERE account_book_id IN ($placeholders)
        AND is_completed = 1
        AND is_locked = 0
    ");
    $stmt->execute($bookIds);

    Response::success(null, '月度重置完成');
}
