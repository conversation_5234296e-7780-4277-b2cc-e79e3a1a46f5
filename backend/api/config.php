<?php

// 设置时区为北京时间
date_default_timezone_set('Asia/Shanghai');

// 环境变量加载函数
function loadEnv($path) {
    if (!file_exists($path)) return;
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// 加载环境变量
loadEnv(__DIR__ . '/../../.env');

// 数据库配置
define('DB_HOST', $_ENV['DB_HOST'] ?? '1Panel-mysql-dPoE');
define('DB_PORT', $_ENV['DB_PORT'] ?? '3306');
define('DB_NAME', $_ENV['DB_NAME'] ?? 'shuju');
define('DB_USER', $_ENV['DB_USER'] ?? 'shuju');
define('DB_PASS', $_ENV['DB_PASS'] ?? 'Abc112211');
define('DB_CHARSET', $_ENV['DB_CHARSET'] ?? 'utf8mb4');

// JWT 配置
define('JWT_SECRET', $_ENV['JWT_SECRET'] ?? 'your-secret-key-change-this-in-production');
define('JWT_ALGORITHM', $_ENV['JWT_ALGORITHM'] ?? 'HS256');

// 跨域配置
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}



// 数据库连接类
class Database {
    private $pdo;

    public function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => 30 // 增加到30秒超时
            ]);
            
            // 设置字符集
            $this->pdo->exec("SET NAMES utf8mb4");
            
        } catch (PDOException $e) {
            // 记录详细的错误信息
            error_log('数据库连接失败: ' . $e->getMessage());
            error_log('连接参数: host=' . DB_HOST . ', port=' . DB_PORT . ', dbname=' . DB_NAME . ', user=' . DB_USER);
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->pdo;
    }
}

// JWT 工具类
class JWT {
    public static function encode($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => JWT_ALGORITHM]);
        $payload = json_encode($payload);

        $headerEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $payloadEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, JWT_SECRET, true);
        $signatureEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }

    public static function decode($jwt) {
        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            throw new Exception('Invalid JWT');
        }

        $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[0]));
        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1]));
        $signature = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[2]));

        $expectedSignature = hash_hmac('sha256', $parts[0] . "." . $parts[1], JWT_SECRET, true);

        if (!hash_equals($signature, $expectedSignature)) {
            throw new Exception('Invalid signature');
        }

        return json_decode($payload, true);
    }
}

// 响应工具类
class Response {
    public static function json($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit();
    }

    public static function error($message, $status = 400) {
        self::json(['error' => $message], $status);
    }

    public static function success($data = null, $message = 'Success') {
        $response = ['message' => $message];
        if ($data !== null) {
            $response['data'] = $data;
        }
        self::json($response);
    }
}

// 获取请求数据
function getRequestData() {
    $input = file_get_contents('php://input');
    return json_decode($input, true) ?: [];
}

// 简单的查询缓存类
class QueryCache {
    private static $cache = [];
    private static $ttl = 300; // 5分钟TTL

    public static function get($key) {
        if (isset(self::$cache[$key])) {
            $item = self::$cache[$key];
            if (time() - $item['timestamp'] < self::$ttl) {
                return $item['data'];
            }
            unset(self::$cache[$key]);
        }
        return null;
    }

    public static function set($key, $data, $ttl = null) {
        self::$cache[$key] = [
            'data' => $data,
            'timestamp' => time(),
            'ttl' => $ttl ?? self::$ttl
        ];
    }

    public static function invalidate($pattern = null) {
        if ($pattern) {
            foreach (array_keys(self::$cache) as $key) {
                if (fnmatch($pattern, $key)) {
                    unset(self::$cache[$key]);
                }
            }
        } else {
            self::$cache = [];
        }
    }

    public static function getStats() {
        return [
            'total_keys' => count(self::$cache),
            'memory_usage' => memory_get_usage(),
            'cache_keys' => array_keys(self::$cache)
        ];
    }
}

// 验证 JWT Token - 增强版本，支持多种header格式
function verifyToken() {
    $headers = getallheaders();
    $authHeader = '';

    // 尝试不同的header名称格式
    foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
        if (isset($headers[$headerName])) {
            $authHeader = $headers[$headerName];
            break;
        }
    }

    // 如果还是没有找到，尝试从$_SERVER中获取
    if (empty($authHeader)) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    }

    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        Response::error('未提供认证令牌', 401);
    }

    try {
        $payload = JWT::decode($matches[1]);
        return $payload;
    } catch (Exception $e) {
        Response::error('无效的认证令牌', 401);
    }
}

// 优化的统计查询函数
function getOptimizedOverviewStats($pdo, $userId) {
    $cacheKey = "overview_stats_{$userId}";
    $cached = QueryCache::get($cacheKey);

    if ($cached !== null) {
        return $cached;
    }

    // 使用单个复杂查询替代多个简单查询
    $stmt = $pdo->prepare("
        SELECT
            COUNT(DISTINCT ab.id) as total_books,
            COUNT(r.id) as total_records,
            COUNT(CASE WHEN r.is_completed = 1 THEN 1 END) as completed_records,
            COALESCE(SUM(r.accumulated_amount), 0) as total_accumulated,
            COALESCE(SUM(CASE WHEN r.is_completed = 1 THEN r.monthly_amount ELSE 0 END), 0) as monthly_income,
            -- 本月统计
            COUNT(CASE WHEN r.completed_month = ? THEN 1 END) as month_records,
            COUNT(CASE WHEN r.is_completed = 1 AND r.completed_month = ? THEN 1 END) as month_completed,
            COALESCE(SUM(CASE WHEN r.is_completed = 1 AND r.completed_month = ? THEN r.monthly_amount ELSE 0 END), 0) as month_income
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ? AND (ab.is_recycle_bin IS NULL OR ab.is_recycle_bin = 0)
    ");

    $currentMonth = date('Y-m');
    $stmt->execute([$currentMonth, $currentMonth, $currentMonth, $userId]);
    $stats = $stmt->fetch();

    QueryCache::set($cacheKey, $stats, 180); // 3分钟缓存
    return $stats;
}

// 优化的记录查询函数 - 解决N+1问题
function getOptimizedRecordsWithMonthlyState($pdo, $bookId, $viewMonth) {
    $cacheKey = "records_with_states_{$bookId}_{$viewMonth}";
    $cached = QueryCache::get($cacheKey);

    if ($cached !== null) {
        return $cached;
    }

    $currentMonth = date('Y-m');

    // 使用JOIN查询，一次性获取所有数据
    $stmt = $pdo->prepare("
        SELECT
            r.*,
            CASE
                WHEN rms.is_completed IS NOT NULL THEN rms.is_completed
                WHEN ? = ? AND r.is_completed = 1 THEN 1
                ELSE 0
            END as current_completed,
            rms.completed_at as monthly_completed_at,
            CASE
                WHEN rms.id IS NOT NULL THEN 'monthly_states'
                WHEN ? = ? THEN 'current_record'
                ELSE 'default'
            END as status_source
        FROM records r
        LEFT JOIN record_monthly_states rms ON r.id = rms.record_id AND rms.view_month = ?
        WHERE r.account_book_id = ?
        ORDER BY r.created_at DESC
    ");

    $stmt->execute([$viewMonth, $currentMonth, $viewMonth, $currentMonth, $viewMonth, $bookId]);
    $records = $stmt->fetchAll();

    QueryCache::set($cacheKey, $records, 120); // 2分钟缓存
    return $records;
}
