<?php

require_once 'Logger.php';
require_once 'Response.php';

/**
 * 统一异常处理类
 */
class ExceptionHandler {
    
    /**
     * 注册异常处理器
     */
    public static function register() {
        // 设置异常处理器
        set_exception_handler([self::class, 'handleException']);
        
        // 设置错误处理器
        set_error_handler([self::class, 'handleError']);
        
        // 设置致命错误处理器
        register_shutdown_function([self::class, 'handleShutdown']);
    }
    
    /**
     * 处理未捕获的异常
     */
    public static function handleException($exception) {
        $message = $exception->getMessage();
        $file = $exception->getFile();
        $line = $exception->getLine();
        $trace = $exception->getTraceAsString();
        
        // 记录异常日志
        Logger::critical("Uncaught Exception: $message", [
            'file' => $file,
            'line' => $line,
            'trace' => $trace
        ]);
        
        // 根据异常类型返回不同的错误响应
        if ($exception instanceof ValidationException) {
            Response::error($message, 400);
        } elseif ($exception instanceof AuthenticationException) {
            Response::error('认证失败', 401);
        } elseif ($exception instanceof AuthorizationException) {
            Response::error('权限不足', 403);
        } elseif ($exception instanceof NotFoundException) {
            Response::error('资源不存在', 404);
        } elseif ($exception instanceof DatabaseException) {
            Logger::error("Database Error: $message", ['trace' => $trace]);
            Response::error('数据库操作失败', 500);
        } else {
            // 通用异常处理
            Response::error('服务器内部错误', 500);
        }
    }
    
    /**
     * 处理PHP错误
     */
    public static function handleError($severity, $message, $file, $line) {
        // 只处理配置的错误级别
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = self::getErrorType($severity);
        
        Logger::error("PHP Error [$errorType]: $message", [
            'file' => $file,
            'line' => $line,
            'severity' => $severity
        ]);
        
        // 对于致命错误，抛出异常
        if (in_array($severity, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            throw new ErrorException($message, 0, $severity, $file, $line);
        }
        
        return true;
    }
    
    /**
     * 处理致命错误
     */
    public static function handleShutdown() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $message = $error['message'];
            $file = $error['file'];
            $line = $error['line'];
            
            Logger::critical("Fatal Error: $message", [
                'file' => $file,
                'line' => $line,
                'type' => $error['type']
            ]);
            
            // 如果还没有输出内容，返回错误响应
            if (!headers_sent()) {
                Response::error('服务器内部错误', 500);
            }
        }
    }
    
    /**
     * 获取错误类型名称
     */
    private static function getErrorType($severity) {
        $errorTypes = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED'
        ];
        
        return $errorTypes[$severity] ?? 'UNKNOWN';
    }
}

/**
 * 自定义异常类
 */
class ValidationException extends Exception {}
class AuthenticationException extends Exception {}
class AuthorizationException extends Exception {}
class NotFoundException extends Exception {}
class DatabaseException extends Exception {}

/**
 * 数据库操作包装器
 */
class DatabaseWrapper {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * 安全执行查询
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute($params);
            
            Logger::logDatabaseOperation('EXECUTE', 'query', [
                'sql' => $sql,
                'params' => $params
            ]);
            
            return $stmt;
        } catch (PDOException $e) {
            Logger::error("Database Query Failed: " . $e->getMessage(), [
                'sql' => $sql,
                'params' => $params
            ]);
            throw new DatabaseException('数据库查询失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        try {
            $this->pdo->beginTransaction();
            Logger::logDatabaseOperation('BEGIN_TRANSACTION', 'transaction');
        } catch (PDOException $e) {
            throw new DatabaseException('开始事务失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        try {
            $this->pdo->commit();
            Logger::logDatabaseOperation('COMMIT', 'transaction');
        } catch (PDOException $e) {
            throw new DatabaseException('提交事务失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        try {
            $this->pdo->rollback();
            Logger::logDatabaseOperation('ROLLBACK', 'transaction');
        } catch (PDOException $e) {
            Logger::error('回滚事务失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取最后插入的ID
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 检查是否在事务中
     */
    public function inTransaction() {
        return $this->pdo->inTransaction();
    }
}
?>
