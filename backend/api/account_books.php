<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            if ($path === '' || $path === '/') {
                getAccountBooks($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'POST':
            if ($path === '' || $path === '/') {
                createAccountBook($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'PUT':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                updateAccountBook($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'DELETE':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                deleteAccountBook($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function getAccountBooks($pdo, $userId) {
    $stmt = $pdo->prepare("
        SELECT ab.*,
               COUNT(r.id) as record_count,
               COALESCE(SUM(r.accumulated_amount), 0) as total_amount,
               ab.is_recycle_bin
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ?
        GROUP BY ab.id
        ORDER BY ab.is_recycle_bin ASC, ab.created_at DESC
    ");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();

    // 为回收站账本添加特殊标识和重新计算累计金额
    foreach ($books as &$book) {
        if ($book['is_recycle_bin']) {
            $book['is_special'] = true;
            $book['type'] = 'recycle_bin';

            // 对于回收站，重新计算累计金额（使用绝对值）
            $recycleBinStmt = $pdo->prepare("
                SELECT COALESCE(SUM(ABS(amount)), 0) as recycle_total_amount
                FROM records
                WHERE account_book_id = ?
            ");
            $recycleBinStmt->execute([$book['id']]);
            $recycleBinStats = $recycleBinStmt->fetch();
            $book['total_amount'] = $recycleBinStats['recycle_total_amount'];
        } else {
            $book['is_special'] = false;
            $book['type'] = 'normal';
        }
    }

    Response::success($books);
}

function createAccountBook($pdo, $userId) {
    $data = getRequestData();

    if (empty($data['name'])) {
        Response::error('账本名称不能为空');
    }
    
    $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
    $stmt->execute([$userId, $data['name'], $data['description'] ?? '']);
    
    $bookId = $pdo->lastInsertId();
    
    $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
    $stmt->execute([$bookId]);
    $book = $stmt->fetch();
    
    Response::success($book, '账本创建成功');
}

function updateAccountBook($pdo, $userId, $bookId) {
    $data = getRequestData();
    
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }
    
    if (empty($data['name'])) {
        Response::error('账本名称不能为空');
    }
    
    $stmt = $pdo->prepare("UPDATE account_books SET name = ?, description = ? WHERE id = ?");
    $stmt->execute([$data['name'], $data['description'] ?? '', $bookId]);
    
    $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
    $stmt->execute([$bookId]);
    $book = $stmt->fetch();
    
    Response::success($book, '账本更新成功');
}

function deleteAccountBook($pdo, $userId, $bookId) {
    // 验证账本所有权并检查是否为回收站账本
    $stmt = $pdo->prepare("SELECT id, is_recycle_bin FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    $book = $stmt->fetch();

    if (!$book) {
        Response::error('账本不存在或无权限', 404);
    }

    // 禁止删除回收站账本
    if ($book['is_recycle_bin']) {
        Response::error('不能删除回收站账本');
    }

    // 检查是否为用户唯一的普通账本
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM account_books WHERE user_id = ? AND is_recycle_bin = 0");
    $stmt->execute([$userId]);
    $count = $stmt->fetch()['count'];

    if ($count <= 1) {
        Response::error('不能删除唯一的账本');
    }

    // 检查账本中是否有记录，如果有则移动到回收站
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM records WHERE account_book_id = ?");
    $stmt->execute([$bookId]);
    $recordCount = $stmt->fetch()['count'];

    if ($recordCount > 0) {
        try {
            $pdo->beginTransaction();

            // 获取或创建回收站账本
            $stmt = $pdo->prepare("
                SELECT * FROM account_books
                WHERE user_id = ? AND is_recycle_bin = 1
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $recycleBin = $stmt->fetch();

            if (!$recycleBin) {
                // 创建回收站账本
                $stmt = $pdo->prepare("
                    INSERT INTO account_books (user_id, name, description, is_recycle_bin)
                    VALUES (?, '🗑️ 回收站', '系统自动创建的回收站，用于存放已删除的记录', 1)
                ");
                $stmt->execute([$userId]);
                $recycleBinId = $pdo->lastInsertId();
            } else {
                $recycleBinId = $recycleBin['id'];
            }

            // 移动所有记录到回收站
            $stmt = $pdo->prepare("
                UPDATE records SET
                    account_book_id = ?,
                    original_book_id = ?,
                    deleted_at = NOW()
                WHERE account_book_id = ?
            ");
            $stmt->execute([$recycleBinId, $bookId, $bookId]);

            // 删除账本
            $stmt = $pdo->prepare("DELETE FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);

            $pdo->commit();

            Response::success([
                'moved_records' => $recordCount,
                'to_recycle_bin' => true
            ], "账本删除成功，{$recordCount} 条记录已移动到回收站");

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            throw $e;
        }
    } else {
        // 没有记录，直接删除账本
        $stmt = $pdo->prepare("DELETE FROM account_books WHERE id = ?");
        $stmt->execute([$bookId]);

        Response::success(null, '账本删除成功');
    }
}
