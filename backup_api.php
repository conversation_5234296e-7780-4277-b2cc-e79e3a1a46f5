<?php
/**
 * 备份管理API
 */

// 开启输出缓冲，防止意外输出
ob_start();

// 设置错误报告级别，避免警告信息混入JSON
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_end_clean();
    exit(0);
}

// 清理之前的输出缓冲
ob_clean();

// 备份目录
$backupDir = __DIR__ . '/Backup';

// 获取操作类型
$action = $_GET['action'] ?? ($_POST['action'] ?? null);
if (!$action) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? null;
}

// 响应函数
function sendResponse($success, $data = null, $message = null) {
    // 清理任何之前的输出
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response = ['success' => $success];
    
    if ($message !== null) {
        $response['message'] = $message;
    }
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 错误处理函数
function handleError($message, $details = null) {
    error_log("Backup API Error: $message" . ($details ? " - Details: $details" : ""));
    sendResponse(false, null, $message);
}

try {
    switch ($action) {
        case 'list_backups':
            listBackups();
            break;

        case 'create_backup':
            createBackup();
            break;

        case 'delete_backup':
            deleteBackup();
            break;

        case 'download':
            downloadBackup();
            break;

        case 'restore_backup':
            restoreBackup();
            break;

        case 'validate_backup':
            validateBackup();
            break;

        case 'validate_backups':
            validateAllBackups();
            break;

        default:
            handleError('无效的操作类型: ' . ($action ?: '未指定'));
    }
} catch (Exception $e) {
    handleError($e->getMessage());
} catch (Error $e) {
    handleError('系统错误: ' . $e->getMessage());
}

/**
 * 列出所有备份
 */
function listBackups() {
    global $backupDir;

    try {
        if (!is_dir($backupDir)) {
            sendResponse(true, []);
            return;
        }

        $backups = [];
        $items = scandir($backupDir);

        if ($items === false) {
            handleError('无法读取备份目录');
            return;
        }

        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;

            $itemPath = $backupDir . '/' . $item;

            if (is_dir($itemPath)) {
                // 目录备份
                try {
                    $size = getDirSize($itemPath);
                    $backupType = getBackupType($itemPath);
                    
                    $backups[] = [
                        'name' => $item,
                        'type' => 'directory',
                        'backup_type' => $backupType,
                        'date' => date('Y-m-d H:i:s', filemtime($itemPath)),
                        'size' => formatBytes($size),
                        'raw_size' => $size
                    ];
                } catch (Exception $e) {
                    error_log("Error processing backup directory $item: " . $e->getMessage());
                    continue;
                }
            } elseif (pathinfo($item, PATHINFO_EXTENSION) === 'zip') {
                // ZIP备份
                try {
                    $backupType = 'unknown'; // ZIP文件无法直接判断类型
                    
                    $backups[] = [
                        'name' => $item,
                        'type' => 'zip',
                        'backup_type' => $backupType,
                        'date' => date('Y-m-d H:i:s', filemtime($itemPath)),
                        'size' => formatBytes(filesize($itemPath)),
                        'raw_size' => filesize($itemPath)
                    ];
                } catch (Exception $e) {
                    error_log("Error processing backup file $item: " . $e->getMessage());
                    continue;
                }
            }
        }

        // 按日期排序（最新的在前）
        usort($backups, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        sendResponse(true, $backups);
        
    } catch (Exception $e) {
        handleError('获取备份列表失败: ' . $e->getMessage());
    }
}

/**
 * 获取备份类型
 */
function getBackupType($backupPath) {
    $infoFile = $backupPath . '/backup_info.json';
    
    if (file_exists($infoFile)) {
        $info = json_decode(file_get_contents($infoFile), true);
        if ($info && isset($info['backup_type'])) {
            return $info['backup_type'];
        }
    }
    
    // 如果没有信息文件，通过文件数量判断
    $fileCount = 0;
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($backupPath, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $fileCount++;
        }
    }
    
    // 根据文件数量粗略判断备份类型
    // 快速备份通常包含10-15个核心文件
    // 完整备份通常包含25+个文件
    return $fileCount <= 20 ? 'quick' : 'full';
}

/**
 * 创建备份
 */
function createBackup() {
    try {
        // 获取备份参数
        $input = json_decode(file_get_contents('php://input'), true);
        $backupType = $input['backup_type'] ?? 'quick';
        $autoCleanup = $input['auto_cleanup'] ?? true;
        $compress = $input['compress'] ?? false;
        
        // 验证备份类型
        if (!in_array($backupType, ['quick', 'full'])) {
            $backupType = 'quick';
        }
        
        // 检查PHP可执行性
        $phpPath = 'php';
        if (!isCommandAvailable($phpPath)) {
            // 尝试常见的PHP路径
            $possiblePaths = ['/usr/bin/php', '/usr/local/bin/php', 'php.exe'];
            $phpPath = null;
            
            foreach ($possiblePaths as $path) {
                if (isCommandAvailable($path)) {
                    $phpPath = $path;
                    break;
                }
            }
            
            if (!$phpPath) {
                handleError('PHP命令行工具不可用，请检查PHP安装');
                return;
            }
        }
        
        // 检查备份脚本是否存在
        $backupScript = __DIR__ . '/quick_backup.php';
        if (!file_exists($backupScript)) {
            handleError('备份脚本不存在: ' . $backupScript);
            return;
        }
        
        // 执行备份脚本
        $output = [];
        $returnCode = 0;
        
        $command = sprintf(
            'cd %s && %s %s %s 2>&1',
            escapeshellarg(__DIR__),
            escapeshellarg($phpPath),
            escapeshellarg($backupScript),
            escapeshellarg($backupType)
        );

        // 设置执行时间限制
        set_time_limit(300); // 5分钟
        
        exec($command, $output, $returnCode);
        
        // 分析输出结果
        $outputText = implode("\n", $output);
        
        if ($returnCode === 0) {
            $message = "备份创建成功 (类型: $backupType)";
            
            // 如果启用了压缩选项，尝试压缩最新的备份
            if ($compress) {
                try {
                    $latestBackup = getLatestBackup();
                    if ($latestBackup) {
                        compressBackup($latestBackup);
                        $message .= " - 已压缩为ZIP文件";
                    }
                } catch (Exception $e) {
                    $message .= " - 压缩失败: " . $e->getMessage();
                }
            }
            
            // 如果启用了自动清理，清理旧备份
            if ($autoCleanup) {
                try {
                    cleanupOldBackups();
                    $message .= " - 已清理旧备份";
                } catch (Exception $e) {
                    $message .= " - 清理失败: " . $e->getMessage();
                }
            }
            
            sendResponse(true, [
                'backup_type' => $backupType,
                'compressed' => $compress,
                'auto_cleanup' => $autoCleanup,
                'output' => $outputText
            ], $message);
            
        } else {
            // 分析错误原因
            $errorMessage = '备份创建失败';
            
            if (strpos($outputText, 'Permission denied') !== false) {
                $errorMessage .= ': 权限不足，请检查目录权限';
            } elseif (strpos($outputText, 'No space left') !== false) {
                $errorMessage .= ': 磁盘空间不足';
            } elseif (strpos($outputText, 'Connection refused') !== false) {
                $errorMessage .= ': 数据库连接失败';
            } elseif (!empty($outputText)) {
                $errorMessage .= ': ' . $outputText;
            } else {
                $errorMessage .= ': 未知错误 (返回码: ' . $returnCode . ')';
            }
            
            handleError($errorMessage);
        }
        
    } catch (Exception $e) {
        handleError('备份创建异常: ' . $e->getMessage());
    }
}

/**
 * 检查命令是否可用
 */
function isCommandAvailable($command) {
    $output = [];
    $returnCode = 0;
    
    // Windows系统
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        exec("where $command 2>nul", $output, $returnCode);
    } else {
        // Unix/Linux系统
        exec("which $command 2>/dev/null", $output, $returnCode);
    }
    
    return $returnCode === 0;
}

/**
 * 清理旧备份
 */
function cleanupOldBackups($keepCount = 10) {
    global $backupDir;
    
    if (!is_dir($backupDir)) {
        return;
    }
    
    $backups = [];
    $items = scandir($backupDir);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $itemPath = $backupDir . '/' . $item;
        if (is_dir($itemPath) || pathinfo($item, PATHINFO_EXTENSION) === 'zip') {
            $backups[] = [
                'name' => $item,
                'path' => $itemPath,
                'time' => filemtime($itemPath)
            ];
        }
    }
    
    // 按时间排序，最新的在前
    usort($backups, function($a, $b) {
        return $b['time'] - $a['time'];
    });
    
    // 删除多余的备份
    $toDelete = array_slice($backups, $keepCount);
    
    foreach ($toDelete as $backup) {
        try {
            if (is_dir($backup['path'])) {
                removeDirectory($backup['path']);
            } else {
                unlink($backup['path']);
            }
        } catch (Exception $e) {
            error_log("Failed to delete old backup {$backup['name']}: " . $e->getMessage());
        }
    }
}

/**
 * 删除备份
 */
function deleteBackup() {
    global $backupDir;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $backupName = $input['backup_name'] ?? $_POST['backup_name'] ?? null;

        if (!$backupName) {
            handleError('未指定备份名称');
            return;
        }

        $backupPath = $backupDir . '/' . $backupName;

        // 安全检查
        if (!file_exists($backupPath)) {
            handleError('备份不存在: ' . $backupName);
            return;
        }
        
        // 路径安全检查
        $realBackupPath = realpath($backupPath);
        $realBackupDir = realpath($backupDir);
        
        if (!$realBackupPath || !$realBackupDir || strpos($realBackupPath, $realBackupDir) !== 0) {
            handleError('备份路径无效或不安全');
            return;
        }

        // 尝试删除
        $deleted = false;
        $errorMessage = '';
        
        try {
            if (is_dir($backupPath)) {
                $deleted = removeDirectory($backupPath);
            } else {
                $deleted = unlink($backupPath);
            }
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
        }
        
        // 如果普通删除失败，尝试使用系统命令
        if (!$deleted && file_exists($backupPath)) {
            $output = [];
            $returnCode = 0;
            
            if (is_dir($backupPath)) {
                // Windows系统
                if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                    $command = sprintf('rmdir /s /q %s 2>&1', escapeshellarg($backupPath));
                } else {
                    $command = sprintf('rm -rf %s 2>&1', escapeshellarg($backupPath));
                }
            } else {
                // Windows系统
                if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                    $command = sprintf('del /f /q %s 2>&1', escapeshellarg($backupPath));
                } else {
                    $command = sprintf('rm -f %s 2>&1', escapeshellarg($backupPath));
                }
            }

            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                $deleted = true;
            } else {
                $errorMessage = implode("\n", $output);
            }
        }

        // 验证删除是否成功
        if (file_exists($backupPath)) {
            handleError('删除失败，文件仍然存在' . ($errorMessage ? ': ' . $errorMessage : ''));
            return;
        }

        sendResponse(true, null, '备份删除成功');
        
    } catch (Exception $e) {
        handleError('删除备份时发生错误: ' . $e->getMessage());
    }
}

/**
 * 下载备份
 */
function downloadBackup() {
    global $backupDir;

    $backupName = $_GET['backup'] ?? null;

    if (!$backupName) {
        throw new Exception('未指定备份名称');
    }

    $backupPath = $backupDir . '/' . $backupName;

    // 安全检查
    if (!file_exists($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
        throw new Exception('备份不存在或路径无效');
    }

    if (is_dir($backupPath)) {
        // 如果是目录，创建临时压缩文件
        $tempDir = sys_get_temp_dir();
        $tarFile = $tempDir . '/' . $backupName . '.tar.gz';

        // 检查临时目录权限
        if (!is_writable($tempDir)) {
            // 使用项目目录作为临时目录
            $tempDir = __DIR__ . '/temp';
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            $tarFile = $tempDir . '/' . $backupName . '.tar.gz';
        }

        // 尝试使用ZipArchive（如果可用）
        if (class_exists('ZipArchive')) {
            $zipFile = $tempDir . '/' . $backupName . '.zip';
            $zip = new ZipArchive();
            if ($zip->open($zipFile, ZipArchive::CREATE) === TRUE) {
                addDirectoryToZip($zip, $backupPath, '');
                $zip->close();

                // 下载ZIP文件
                header('Content-Type: application/zip');
                header('Content-Disposition: attachment; filename="' . $backupName . '.zip"');
                header('Content-Length: ' . filesize($zipFile));
                readfile($zipFile);

                // 删除临时文件
                unlink($zipFile);
                exit;
            }
        }

        // 备用方案：使用tar命令
        $command = sprintf(
            'cd %s && tar -czf %s %s 2>&1',
            escapeshellarg(dirname($backupPath)),
            escapeshellarg($tarFile),
            escapeshellarg(basename($backupPath))
        );

        exec($command, $output, $returnCode);

        if ($returnCode === 0 && file_exists($tarFile)) {
            // 下载tar.gz文件
            header('Content-Type: application/gzip');
            header('Content-Disposition: attachment; filename="' . $backupName . '.tar.gz"');
            header('Content-Length: ' . filesize($tarFile));
            readfile($tarFile);

            // 删除临时文件
            unlink($tarFile);
            exit;
        }

        // 最后的备用方案：创建简单的文本文件列表
        $listFile = $tempDir . '/' . $backupName . '_filelist.txt';
        $fileList = "备份目录: $backupName\n";
        $fileList .= "创建时间: " . date('Y-m-d H:i:s') . "\n\n";
        $fileList .= "文件列表:\n";

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backupPath, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            $relativePath = substr($file->getPathname(), strlen($backupPath) + 1);
            $fileList .= $relativePath . " (" . formatBytes($file->getSize()) . ")\n";
        }

        file_put_contents($listFile, $fileList);

        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="' . $backupName . '_filelist.txt"');
        header('Content-Length: ' . filesize($listFile));
        readfile($listFile);

        unlink($listFile);
        exit;
    } else {
        // 直接下载文件
        $mimeType = 'application/octet-stream';
        if (pathinfo($backupName, PATHINFO_EXTENSION) === 'zip') {
            $mimeType = 'application/zip';
        }

        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . basename($backupName) . '"');
        header('Content-Length: ' . filesize($backupPath));
        readfile($backupPath);
        exit;
    }
}

/**
 * 计算目录大小
 */
function getDirSize($dir) {
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));

    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }

    return $size;
}

/**
 * 格式化字节大小
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * 删除目录
 */
function removeDirectory($dir) {
    if (!is_dir($dir)) return;

    try {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isDir()) {
                if (!rmdir($file->getPathname())) {
                    throw new Exception('无法删除目录: ' . $file->getPathname());
                }
            } else {
                if (!unlink($file->getPathname())) {
                    throw new Exception('无法删除文件: ' . $file->getPathname());
                }
            }
        }

        if (!rmdir($dir)) {
            throw new Exception('无法删除根目录: ' . $dir);
        }
    } catch (Exception $e) {
        throw new Exception('删除目录失败: ' . $e->getMessage());
    }
}

/**
 * 恢复备份
 */
function restoreBackup() {
    global $backupDir;

    $input = json_decode(file_get_contents('php://input'), true);
    $backupName = $input['backup_name'] ?? null;
    $restoreType = $input['restore_type'] ?? 'full';

    if (!$backupName) {
        handleError('未指定备份名称');
        return;
    }

    $backupPath = $backupDir . '/' . $backupName;

    // 安全检查
    if (!is_dir($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
        handleError('备份不存在或路径无效');
        return;
    }

    // 数据库配置
    $dbConfig = [
        'host' => '1Panel-mysql-dPoE',
        'port' => '3306',
        'name' => 'shuju',
        'user' => 'shuju',
        'pass' => 'Abc112211'
    ];

    $results = [];

    try {
        // 1. 创建当前状态的备份（安全措施）
        $currentBackupName = 'before_restore_' . date('Y-m-d_H-i-s');
        $currentBackupDir = $backupDir . '/' . $currentBackupName;
        mkdir($currentBackupDir, 0755, true);

        // 备份当前数据库
        if ($restoreType === 'full' || $restoreType === 'database') {
            $currentDbFile = $currentBackupDir . '/current_database.sql';
            $command = sprintf(
                'mysqldump -h%s -P%s -u%s -p%s %s > %s 2>&1',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['port']),
                escapeshellarg($dbConfig['user']),
                escapeshellarg($dbConfig['pass']),
                escapeshellarg($dbConfig['name']),
                escapeshellarg($currentDbFile)
            );
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                $results[] = '✓ 当前数据库已备份';
            }
        }

        // 备份当前重要文件
        if ($restoreType === 'full' || $restoreType === 'files') {
            $importantFiles = ['index.html', 'api_direct.php', '.htaccess'];
            foreach ($importantFiles as $file) {
                $sourcePath = __DIR__ . '/' . $file;
                $destPath = $currentBackupDir . '/' . $file;
                if (file_exists($sourcePath)) {
                    copy($sourcePath, $destPath);
                }
            }
            $results[] = '✓ 当前文件已备份';
        }

        // 2. 执行恢复操作
        if ($restoreType === 'full' || $restoreType === 'database') {
            // 恢复数据库
            $sqlFile = $backupPath . '/database.sql';
            if (file_exists($sqlFile)) {
                $command = sprintf(
                    'mysql -h%s -P%s -u%s -p%s %s < %s 2>&1',
                    escapeshellarg($dbConfig['host']),
                    escapeshellarg($dbConfig['port']),
                    escapeshellarg($dbConfig['user']),
                    escapeshellarg($dbConfig['pass']),
                    escapeshellarg($dbConfig['name']),
                    escapeshellarg($sqlFile)
                );

                exec($command, $dbOutput, $dbReturnCode);

                if ($dbReturnCode === 0) {
                    $results[] = '✓ 数据库恢复成功';
                } else {
                    handleError('数据库恢复失败: ' . implode("\n", $dbOutput));
                    return;
                }
            } else {
                handleError('备份中未找到数据库文件');
                return;
            }
        }

        if ($restoreType === 'full' || $restoreType === 'files') {
            // 恢复文件
            $restoredCount = 0;
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backupPath, RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getFilename() !== 'database.sql' && $file->getFilename() !== 'backup_info.txt') {
                    $relativePath = substr($file->getPathname(), strlen($backupPath) + 1);
                    $destPath = __DIR__ . '/' . $relativePath;

                    // 创建目标目录
                    $destDir = dirname($destPath);
                    if (!is_dir($destDir)) {
                        mkdir($destDir, 0755, true);
                    }

                    if (copy($file->getPathname(), $destPath)) {
                        $restoredCount++;
                    }
                }
            }

            $results[] = "✓ 恢复了 $restoredCount 个文件";
        }

        // 3. 创建恢复信息文件
        $restoreInfo = [
            'restore_time' => date('Y-m-d H:i:s'),
            'backup_name' => $backupName,
            'restore_type' => $restoreType,
            'current_backup' => $currentBackupName,
            'results' => $results
        ];

        file_put_contents(
            $currentBackupDir . '/restore_info.json',
            json_encode($restoreInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );

        sendResponse(true, [
            'results' => $results,
            'current_backup' => $currentBackupName
        ], '恢复操作完成');

    } catch (Exception $e) {
        // 恢复失败时的清理
        if (isset($currentBackupDir) && is_dir($currentBackupDir)) {
            // 保留失败前的备份，但记录错误
            file_put_contents(
                $currentBackupDir . '/restore_error.log',
                date('Y-m-d H:i:s') . " - 恢复失败: " . $e->getMessage()
            );
        }

        handleError('恢复操作失败: ' . $e->getMessage());
    }
}

/**
 * 添加目录到ZIP
 */
function addDirectoryToZip($zip, $source, $prefix) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS)
    );

    foreach ($iterator as $file) {
        $relativePath = $prefix . substr($file->getPathname(), strlen($source) + 1);

        if ($file->isFile()) {
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }
}

/**
 * 验证单个备份
 */
function validateBackup() {
    global $backupDir;

    $input = json_decode(file_get_contents('php://input'), true);
    $backupName = $input['backup_name'] ?? null;

    if (!$backupName) {
        throw new Exception('未指定备份名称');
    }

    $backupPath = $backupDir . '/' . $backupName;

    // 安全检查
    if (!file_exists($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
        throw new Exception('备份不存在或路径无效');
    }

    $validationResult = [
        'is_valid' => true,
        'checks' => [],
        'error' => null
    ];

    try {
        if (is_dir($backupPath)) {
            // 验证目录备份
            $validationResult = validateDirectoryBackup($backupPath);
        } elseif (pathinfo($backupPath, PATHINFO_EXTENSION) === 'zip') {
            // 验证ZIP备份
            $validationResult = validateZipBackup($backupPath);
        } else {
            $validationResult['is_valid'] = false;
            $validationResult['error'] = '不支持的备份格式';
        }

        echo json_encode([
            'success' => true,
            'data' => $validationResult
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 验证所有备份
 */
function validateAllBackups() {
    global $backupDir;

    if (!is_dir($backupDir)) {
        echo json_encode([
            'success' => true,
            'data' => [
                'total_count' => 0,
                'valid_count' => 0,
                'invalid_backups' => []
            ]
        ]);
        return;
    }

    $totalCount = 0;
    $validCount = 0;
    $invalidBackups = [];

    $items = scandir($backupDir);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;

        $itemPath = $backupDir . '/' . $item;

        if (is_dir($itemPath) || pathinfo($item, PATHINFO_EXTENSION) === 'zip') {
            $totalCount++;

            try {
                if (is_dir($itemPath)) {
                    $result = validateDirectoryBackup($itemPath);
                } else {
                    $result = validateZipBackup($itemPath);
                }

                if ($result['is_valid']) {
                    $validCount++;
                } else {
                    $invalidBackups[] = [
                        'name' => $item,
                        'error' => $result['error']
                    ];
                }
            } catch (Exception $e) {
                $invalidBackups[] = [
                    'name' => $item,
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'total_count' => $totalCount,
            'valid_count' => $validCount,
            'invalid_backups' => $invalidBackups
        ]
    ]);
}

/**
 * 验证目录备份
 */
function validateDirectoryBackup($backupPath) {
    $result = [
        'is_valid' => true,
        'checks' => [],
        'error' => null
    ];

    // 检查备份信息文件
    $infoFile = $backupPath . '/backup_info.json';
    if (file_exists($infoFile)) {
        $result['checks'][] = '✓ 备份信息文件存在';
        
        $info = json_decode(file_get_contents($infoFile), true);
        if ($info) {
            $result['checks'][] = '✓ 备份信息文件格式正确';
        } else {
            $result['is_valid'] = false;
            $result['error'] = '备份信息文件格式错误';
            return $result;
        }
    } else {
        $result['checks'][] = '⚠ 备份信息文件缺失';
    }

    // 检查数据库备份文件
    $dbFile = $backupPath . '/database.sql';
    if (file_exists($dbFile)) {
        if (filesize($dbFile) > 0) {
            $result['checks'][] = '✓ 数据库备份文件存在且非空';
        } else {
            $result['checks'][] = '⚠ 数据库备份文件为空';
        }
    } else {
        $result['checks'][] = '⚠ 数据库备份文件缺失';
    }

    // 检查核心文件
    $coreFiles = ['index.html', 'api_direct.php'];
    $coreFileCount = 0;
    foreach ($coreFiles as $file) {
        if (file_exists($backupPath . '/' . $file)) {
            $coreFileCount++;
        }
    }

    if ($coreFileCount > 0) {
        $result['checks'][] = "✓ 核心文件: $coreFileCount/" . count($coreFiles);
    } else {
        $result['is_valid'] = false;
        $result['error'] = '缺少核心文件';
        return $result;
    }

    // 检查目录权限
    if (!is_readable($backupPath)) {
        $result['is_valid'] = false;
        $result['error'] = '备份目录不可读';
        return $result;
    }

    $result['checks'][] = '✓ 目录权限正常';

    return $result;
}

/**
 * 验证ZIP备份
 */
function validateZipBackup($zipPath) {
    $result = [
        'is_valid' => true,
        'checks' => [],
        'error' => null
    ];

    // 检查文件是否存在且可读
    if (!is_readable($zipPath)) {
        $result['is_valid'] = false;
        $result['error'] = 'ZIP文件不可读';
        return $result;
    }

    $result['checks'][] = '✓ ZIP文件可读';

    // 检查ZIP文件完整性
    $zip = new ZipArchive();
    $zipResult = $zip->open($zipPath, ZipArchive::CHECKCONS);

    if ($zipResult !== TRUE) {
        $result['is_valid'] = false;
        $result['error'] = 'ZIP文件损坏或格式错误: ' . $zipResult;
        return $result;
    }

    $result['checks'][] = '✓ ZIP文件完整性验证通过';

    // 检查ZIP内容
    $hasDatabase = false;
    $hasCoreFiles = false;

    for ($i = 0; $i < $zip->numFiles; $i++) {
        $filename = $zip->getNameIndex($i);
        
        if (strpos($filename, 'database.sql') !== false) {
            $hasDatabase = true;
        }
        
        if (strpos($filename, 'index.html') !== false || 
            strpos($filename, 'api_direct.php') !== false) {
            $hasCoreFiles = true;
        }
    }

    if ($hasDatabase) {
        $result['checks'][] = '✓ 包含数据库备份';
    } else {
        $result['checks'][] = '⚠ 缺少数据库备份';
    }

    if ($hasCoreFiles) {
        $result['checks'][] = '✓ 包含核心文件';
    } else {
        $result['is_valid'] = false;
        $result['error'] = '缺少核心文件';
        $zip->close();
        return $result;
    }

    $zip->close();
    $result['checks'][] = '✓ ZIP内容验证完成';

    return $result;
}

/**
 * 获取最新的备份目录
 */
function getLatestBackup() {
    global $backupDir;
    
    if (!is_dir($backupDir)) {
        return null;
    }
    
    $latestTime = 0;
    $latestBackup = null;
    
    $items = scandir($backupDir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $itemPath = $backupDir . '/' . $item;
        if (is_dir($itemPath) && strpos($item, 'backup_') === 0) {
            $time = filemtime($itemPath);
            if ($time > $latestTime) {
                $latestTime = $time;
                $latestBackup = $item;
            }
        }
    }
    
    return $latestBackup;
}

/**
 * 压缩备份目录为ZIP文件
 */
function compressBackup($backupName) {
    global $backupDir;
    
    $backupPath = $backupDir . '/' . $backupName;
    $zipPath = $backupDir . '/' . $backupName . '.zip';
    
    if (!is_dir($backupPath)) {
        throw new Exception('备份目录不存在');
    }
    
    if (!class_exists('ZipArchive')) {
        throw new Exception('ZipArchive扩展不可用');
    }
    
    $zip = new ZipArchive();
    $result = $zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
    
    if ($result !== TRUE) {
        throw new Exception('无法创建ZIP文件: ' . $result);
    }
    
    // 添加目录内容到ZIP
    addDirectoryToZip($zip, $backupPath, '');
    $zip->close();
    
    // 验证ZIP文件创建成功
    if (!file_exists($zipPath)) {
        throw new Exception('ZIP文件创建失败');
    }
    
    // 删除原始目录
    removeDirectory($backupPath);
    
    return $zipPath;
}
?>
